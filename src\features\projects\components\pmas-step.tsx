'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { SectionHeader } from '@/components/ui/section-header';
import { zodResolver } from '@hookform/resolvers/zod';
import { ChevronLeft, Plus, Save } from 'lucide-react';
import { useFieldArray, useForm, UseFormReturn } from 'react-hook-form';
import { pmasSchema, type PmasSchema } from '../schemas/project-schema';
import { PmaCard } from './pma-card';

interface PmasStepProps {
  form: UseFormReturn<PmasSchema>;
  onSubmit: (data: PmasSchema) => Promise<void>;
  onPrevious: () => void;
  onCancel?: () => void;
  isLoading?: boolean;
}

/**
 * PMAs Step - Final step in the multi-step project creation form
 * Handles multiple Project Management Authority (PMA) information and final submission
 */
export function PmasStep({
  form,
  onSubmit,
  onPrevious,
  onCancel,
  isLoading,
}: PmasStepProps) {
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'pmas',
  });

  const handleSubmit = (data: PmasSchema) => {
    console.log('PMAs form submission:', data);
    console.log('Form errors:', form.formState.errors);
    console.log('Form is valid:', form.formState.isValid);
    onSubmit(data);
  };

  const addPma = () => {
    append({
      expiry_date: '',
      location: '',
      file: new File([], ''), // Temporary empty file until user uploads
    });
  };

  const removePma = (index: number) => {
    remove(index);
  };

  return (
    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-7">
      <SectionHeader
        title="PMA Information"
        description="Provide the Project Management Authority (PMA) details for this project."
        number={2}
      />

      <div className="space-y-6">
        {/* PMAs List */}
        {fields.map((field, index) => (
          <PmaCard
            key={field.id}
            form={form}
            index={index}
            onRemove={() => removePma(index)}
            canRemove={fields.length > 1}
          />
        ))}

        {/* Array-level validation errors */}
        {form.formState.errors.pmas && (
          <p className="text-sm text-destructive">
            {form.formState.errors.pmas.message}
          </p>
        )}

        {/* Add PMA Button */}
        <Button
          type="button"
          variant="outline"
          onClick={addPma}
          className="w-full"
          disabled={fields.length >= 5}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Another PMA
        </Button>
      </div>

      {/* Actions */}
      <div className="flex gap-4 pt-6">
        <Button type="button" variant="outline" onClick={onPrevious}>
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        <Button type="submit" disabled={isLoading} className="min-w-32">
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              Creating...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Create Project
            </>
          )}
        </Button>
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}
      </div>
    </form>
  );
}

/**
 * Hook to create and manage the PMAs form (multiple)
 * @param initialData Optional initial data for the form
 * @returns Form instance and helper functions
 */
export function usePmasForm(initialData?: Partial<PmasSchema>) {
  return useForm<PmasSchema>({
    resolver: zodResolver(pmasSchema),
    defaultValues: {
      pmas: initialData?.pmas || [
        {
          expiry_date: '',
          location: '',
          file: new File([], ''), // Temporary empty file until user uploads
        },
      ],
    },
  });
}
