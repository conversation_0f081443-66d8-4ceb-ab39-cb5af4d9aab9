'use client';

import * as React from 'react';
import { RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';

interface CompanyRegistrationOptionProps {
  value: string;
  id: string;
  icon: React.ReactNode;
  title: string;
  description: string;
}

export function CompanyRegistrationOption({
  value,
  id,
  icon,
  title,
  description,
}: CompanyRegistrationOptionProps) {
  return (
    <div className="relative flex w-full items-start gap-4 rounded-xl border-2 border-border p-4 shadow-sm hover:border-border/80 has-[[data-state=checked]]:border-primary has-[[data-state=checked]]:bg-muted/50 transition-colors">
      <RadioGroupItem
        value={value}
        id={id}
        className="order-1 after:absolute after:inset-0 mt-1"
      />
      <div className="flex grow items-start gap-3">
        <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-1">
          {icon}
        </div>
        <div className="grid grow gap-1">
          <Label
            htmlFor={id}
            className="text-base font-semibold text-foreground"
          >
            {title}
          </Label>
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>
      </div>
    </div>
  );
}
