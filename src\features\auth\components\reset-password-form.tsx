'use client';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  useAuthTranslations,
  useValidationTranslations,
} from '@/hooks/use-translations';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { Info, Shield } from 'lucide-react';
import Link from 'next/link';
import * as React from 'react';
import { useState } from 'react';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { toast } from 'sonner';
import { useUpdatePassword } from '../hooks/use-auth';
import {
  createResetPasswordSchema,
  type ResetPasswordFormValues,
} from '../schemas';

type ResetPasswordFormProps = React.HTMLAttributes<HTMLDivElement>;

export function ResetPasswordForm({
  className,
  ...props
}: ResetPasswordFormProps) {
  const [errorMessage, setErrorMessage] = useState<string>('');
  const updatePasswordMutation = useUpdatePassword();
  const auth = useAuthTranslations();
  const validation = useValidationTranslations();

  const formSchema = createResetPasswordSchema(validation, auth);

  type FormValues = ResetPasswordFormValues;

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  });

  const onSubmit: SubmitHandler<FormValues> = async (values) => {
    try {
      setErrorMessage('');

      // Show loading toast
      const loadingToast = toast.loading(auth('updatingPassword'));

      await updatePasswordMutation.mutateAsync(values.password);

      // Dismiss loading toast and show success
      toast.dismiss(loadingToast);
      toast.success(auth('passwordUpdated'));
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : auth('passwordUpdateFailed');

      setErrorMessage(errorMessage);
      toast.error(errorMessage);
    }
  };

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <div className="flex flex-col items-center gap-4 text-center">
        <div className="flex h-16 w-16 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20">
          <Shield className="h-8 w-8 text-green-600 dark:text-green-400" />
        </div>
        <div className="space-y-2">
          <h1 className="text-2xl font-bold">{auth('resetPasswordTitle')}</h1>
          <p className="text-muted-foreground text-sm text-balance">
            {auth('resetPasswordSubtitle')}
          </p>
        </div>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid gap-6">
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem className="grid gap-3">
                  <div className="flex items-center gap-2">
                    <FormLabel>{auth('newPassword')}</FormLabel>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p className="font-medium mb-2">
                          {auth('passwordRequirements')}
                        </p>
                        <ul className="text-sm space-y-1">
                          <li>• {auth('passwordReq1')}</li>
                          <li>• {auth('passwordReq2')}</li>
                          <li>• {auth('passwordReq3')}</li>
                          <li>• {auth('passwordReq4')}</li>
                          <li>• {auth('passwordReq5')}</li>
                        </ul>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <FormControl>
                    <Input
                      placeholder={auth('newPasswordPlaceholder')}
                      type="password"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem className="grid gap-3">
                  <FormLabel>{auth('confirmNewPassword')}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={auth('confirmPasswordPlaceholder')}
                      type="password"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              type="submit"
              className="w-full"
              disabled={updatePasswordMutation.isPending}
            >
              {updatePasswordMutation.isPending
                ? auth('updatingPassword')
                : auth('updatePassword')}
            </Button>
            {errorMessage && (
              <p className="text-sm text-red-600 text-center">{errorMessage}</p>
            )}
          </div>
          <div className="text-center text-sm">
            <Link
              href="/admin/login"
              className="underline underline-offset-4 hover:no-underline"
            >
              {auth('backToLogin')}
            </Link>
          </div>
        </form>
      </Form>
    </div>
  );
}
