import { useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';

/**
 * Hook to force refresh all profile-related data
 * Useful after completing onboarding or other profile changes
 */
export function useForceProfileRefresh() {
  const queryClient = useQueryClient();

  const forceRefresh = useCallback(async () => {
    try {
      // Remove all cached data for profile-related queries
      queryClient.removeQueries({ queryKey: ['user-with-profile'] });
      queryClient.removeQueries({ queryKey: ['profile'] });
      queryClient.removeQueries({ queryKey: ['permissions'] });
      queryClient.removeQueries({ queryKey: ['contractor-profile'] });
      queryClient.removeQueries({ queryKey: ['user'] });

      // Invalidate all related queries
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['user-with-profile'] }),
        queryClient.invalidateQueries({ queryKey: ['profile'] }),
        queryClient.invalidateQueries({ queryKey: ['contractor-profile'] }),
        queryClient.invalidateQueries({ queryKey: ['contractor-companies'] }),
        queryClient.invalidateQueries({ queryKey: ['contractor-pics'] }),
        queryClient.invalidateQueries({ queryKey: ['permissions'] }),
        queryClient.invalidateQueries({ queryKey: ['user'] }),
      ]);

      // Force refetch critical queries
      await Promise.all([
        queryClient.refetchQueries({ queryKey: ['user-with-profile'] }),
        queryClient.refetchQueries({ queryKey: ['permissions'] }),
      ]);

      return true;
    } catch (error) {
      console.error('Error during profile refresh:', error);
      return false;
    }
  }, [queryClient]);

  return forceRefresh;
}
