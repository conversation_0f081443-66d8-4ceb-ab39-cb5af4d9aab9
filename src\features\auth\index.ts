// Auth Feature Exports

// Components
export { AdminLoginForm } from './components/admin-login-form';
export { AdminRegisterForm } from './components/admin-register-form';
export { ContractorLoginForm } from './components/contractor-login-form';
export { ContractorRegisterForm } from './components/contractor-register-form';
export { ForgotPasswordForm } from './components/forgot-password-form';
export { ResetPasswordForm } from './components/reset-password-form';
export { VerificationCodeForm } from './components/verification-code-form';

// Hooks
export {
  useAuthStateChange,
  useLogin,
  useLogout,
  usePasswordReset,
  useSession,
  useSignUp,
  useUpdatePassword,
  useUser,
  useUserWithProfile,
  useVerifyOtp,
} from './hooks/use-auth';

// Types
export type {
  AuthResponse,
  CreateProfilePayload,
  LoginCredentials,
  PasswordResetPayload,
  Profile,
  ProfileInsert,
  ProfileUpdate,
  SignUpCredentials,
  UserRole,
  UserWithProfile,
} from './types/auth';
