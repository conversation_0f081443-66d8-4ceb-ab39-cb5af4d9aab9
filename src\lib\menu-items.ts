import type { PermissionMenuitem } from '@/types/rbac';
import {
  ArrowUpDown,
  BarChart3,
  Building,
  Building2,
  Calendar,
  FileSignature,
  FileText,
  FolderOpen,
  MessageSquare,
  Settings,
  Shield,
  User,
  UserCheck,
  Users,
  UserX,
} from 'lucide-react';

// Lift Management System menu items with their permission requirements
export const MENU_ITEMS: PermissionMenuitem[] = [
  {
    title: 'projects',
    url: '/projects',
    icon: FolderOpen,
    permission: 'projects.view',
    roles: ['admin', 'contractor'],
    description: 'descriptions.projects',
  },
  {
    title: 'lifts',
    url: '/lifts',
    icon: ArrowUpDown,
    permission: 'lifts.view',
    roles: ['admin', 'contractor'],
    description: 'descriptions.lifts',
  },
  {
    title: 'buildings',
    url: '/buildings',
    icon: Building2,
    permission: 'buildings.view',
    roles: ['admin', 'contractor'],
    description: 'descriptions.buildings',
  },
  {
    title: 'contracts',
    url: '/contracts',
    icon: FileSignature,
    permission: 'contracts.view',
    roles: ['admin', 'contractor'],
    description: 'descriptions.contracts',
  },
  {
    title: 'dailyLogs',
    url: '/daily-logs',
    icon: Calendar,
    permission: 'daily_logs.view',
    roles: ['admin', 'contractor', 'viewer'],
    description: 'descriptions.dailyLogs',
  },
  {
    title: 'pmas',
    url: '/pmas',
    icon: Shield,
    permission: 'pmas.view',
    roles: ['admin', 'contractor'],
    description: 'descriptions.pmas',
  },
  {
    title: 'complaints',
    url: '/complaints',
    icon: MessageSquare,
    permission: 'complaints.view',
    roles: ['admin', 'contractor', 'viewer'],
    description: 'descriptions.complaints',
  },
  {
    title: 'contractors',
    url: '/contractors',
    icon: UserCheck,
    permission: 'contractors.view',
    roles: ['admin'],
    description: 'descriptions.contractors',
  },
  {
    title: 'clients',
    url: '/clients',
    icon: Building,
    permission: 'clients.view',
    roles: ['admin', 'contractor'],
    description: 'descriptions.clients',
  },
  {
    title: 'blacklist',
    url: '/blacklist',
    icon: UserX,
    permission: 'blacklist.view',
    roles: ['admin'],
    description: 'descriptions.blacklist',
  },
  {
    title: 'analytics',
    url: '/analytics',
    icon: BarChart3,
    permission: 'analytics.view',
    roles: ['admin', 'contractor'],
    description: 'descriptions.analytics',
  },
  {
    title: 'reports',
    url: '/reports',
    icon: FileText,
    permission: 'reports.view',
    roles: ['admin', 'contractor'],
    description: 'descriptions.reports',
  },
  {
    title: 'users',
    url: '/users',
    icon: Users,
    permission: 'users.view',
    roles: ['admin'],
    description: 'descriptions.users',
  },
  {
    title: 'profile',
    url: '/profile',
    icon: User,
    permission: 'profile.view',
    roles: ['admin', 'contractor', 'viewer'],
    description: 'descriptions.profile',
  },
  {
    title: 'settings',
    url: '/settings',
    icon: Settings,
    permission: 'settings.view',
    roles: ['admin'],
    description: 'descriptions.settings',
  },
];
