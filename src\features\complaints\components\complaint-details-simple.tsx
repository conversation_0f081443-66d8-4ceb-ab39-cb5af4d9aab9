'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  Download,
  FileText,
  Calendar,
  MapPin,
  Building,
  User,
  Phone,
  Mail,
  Edit,
} from 'lucide-react';
import { ComplaintUI } from '../types/ui-types';

interface ComplaintDetailsProps {
  complaint: ComplaintUI;
  onClose: () => void;
  onEdit?: () => void;
}

export function ComplaintDetails({
  complaint,
  onClose,
  onEdit,
}: ComplaintDetailsProps) {
  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-MY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const downloadFile = (fileUrl: string, fileName: string) => {
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    link.click();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Complaint #{complaint.complaintNumber}
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            Submitted on {formatDate(complaint.createdAt)}
          </p>
        </div>{' '}
        <div className="flex items-center gap-3">
          <Badge className={getStatusColor(complaint.status)}>
            {complaint.status.replace('_', ' ').toUpperCase()}
          </Badge>
          {onEdit && (
            <Button variant="outline" onClick={onEdit}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </div>

      <Separator />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3">
              <Mail className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm font-medium">Email</p>
                <p className="text-sm text-gray-600">{complaint.email}</p>
              </div>
            </div>
            {complaint.contactNumber && (
              <div className="flex items-center gap-3">
                <Phone className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Contact Number</p>
                  <p className="text-sm text-gray-600">
                    {complaint.contactNumber}
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Damage Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Damage Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-3">
              <Calendar className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm font-medium">Damage Date</p>
                <p className="text-sm text-gray-600">
                  {formatDate(complaint.damageComplaintDate)}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Calendar className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm font-medium">Expected Completion</p>
                <p className="text-sm text-gray-600">
                  {formatDate(complaint.expectedCompletionDate)}
                </p>
              </div>
            </div>
            {complaint.actualCompletionDate && (
              <div className="flex items-center gap-3">
                <Calendar className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Actual Completion</p>
                  <p className="text-sm text-gray-600">
                    {formatDate(complaint.actualCompletionDate)}
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Location Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Location Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium">Location</p>
              <p className="text-sm text-gray-600">{complaint.location}</p>
            </div>
            {complaint.mantrapLocation && (
              <div>
                <p className="text-sm font-medium">Mantrap Location</p>
                <p className="text-sm text-gray-600">
                  {complaint.mantrapLocation}
                </p>
              </div>
            )}
            {complaint.noPmaLif && (
              <div>
                <p className="text-sm font-medium">NO PMA LIF</p>
                <p className="text-sm text-gray-600">{complaint.noPmaLif}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Agency & Contractor Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Agency & Contractor
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium">Agency</p>
              <p className="text-sm text-gray-600">{complaint.agency}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Contractor/Company Name</p>
              <p className="text-sm text-gray-600">
                {complaint.contractorCompanyName}
              </p>
            </div>
            {complaint.contractorEmail && (
              <div>
                <p className="text-sm font-medium">Contractor Email</p>
                <p className="text-sm text-gray-600">
                  {complaint.contractorEmail}
                </p>
              </div>
            )}
            {complaint.contractorContactNumber && (
              <div>
                <p className="text-sm font-medium">Contractor Contact</p>
                <p className="text-sm text-gray-600">
                  {complaint.contractorContactNumber}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Description */}
      {complaint.description && (
        <Card>
          <CardHeader>
            <CardTitle>Description</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 whitespace-pre-wrap">
              {complaint.description}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Completion Notes */}
      {complaint.completionNotes && (
        <Card>
          <CardHeader>
            <CardTitle>Completion Notes</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 whitespace-pre-wrap">
              {complaint.completionNotes}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Proof of Repair Files */}
      {complaint.proofOfRepairFiles &&
        complaint.proofOfRepairFiles.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Proof of Repair Files
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {complaint.proofOfRepairFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50"
                  >
                    <FileText className="h-5 w-5 text-blue-500" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {file.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => downloadFile(file.url, file.name)}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
    </div>
  );
}
