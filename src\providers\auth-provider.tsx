'use client';

import { createContext, useContext, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuthStateChange } from '@/features/auth';

const AuthContext = createContext<{}>({});

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { handleAuthStateChange } = useAuthStateChange();

  useEffect(() => {
    // Listen for auth state changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      handleAuthStateChange(event, session);
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, [handleAuthStateChange]);

  return <AuthContext.Provider value={{}}>{children}</AuthContext.Provider>;
}

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
};
