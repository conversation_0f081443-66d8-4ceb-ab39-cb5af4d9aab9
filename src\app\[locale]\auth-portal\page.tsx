import { LanguageSwitcher } from '@/components/language-switcher';
import { Button } from '@/components/ui/button';
import {
  ArrowR<PERSON>,
  BookOpen,
  Building2,
  Calendar,
  ChevronDown,
  GalleryVerticalEnd,
  Home,
  Info,
  Mail,
  Send,
  Settings,
  Shield,
  Users,
  Zap,
} from 'lucide-react';
import { getTranslations } from 'next-intl/server';
import Image from 'next/image';
import Link from 'next/link';

export default async function AuthPortalPage() {
  const t = await getTranslations('authPortal');
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* Top Navigation */}
      <header className="fixed top-0 w-full z-50 bg-white/95 backdrop-blur-md border-b border-gray-200/50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center gap-8">
              <div className="flex items-center gap-2 font-bold text-xl">
                <div className="bg-primary text-primary-foreground flex size-8 items-center justify-center rounded-lg">
                  <GalleryVerticalEnd className="size-5" />
                </div>
                SimPLE
              </div>
              {/* Navigation Links */}
              <nav className="hidden md:flex items-center gap-6">
                <Button asChild variant="ghost" size="sm" className="gap-2">
                  <Link href="#home">
                    <Home className="w-4 h-4" />
                    {t('nav.home')}
                  </Link>
                </Button>
                <Button asChild variant="ghost" size="sm" className="gap-2">
                  <Link href="#what-is-simple">
                    <Info className="w-4 h-4" />
                    {t('nav.whatIsSimple')}
                  </Link>
                </Button>
                <Button asChild variant="ghost" size="sm" className="gap-2">
                  <Link href="#about">
                    <Info className="w-4 h-4" />
                    {t('nav.about')}
                  </Link>
                </Button>
                <Button asChild variant="ghost" size="sm" className="gap-2">
                  <Link href="#contact">
                    <Mail className="w-4 h-4" />
                    {t('nav.contact')}
                  </Link>
                </Button>
              </nav>
            </div>
            {/* Top Action Buttons */}
            <div className="flex items-center gap-3">
              {/* Language Switcher - Desktop */}
              <div className="w-36 hidden sm:block">
                <LanguageSwitcher />
              </div>

              {/* Language Switcher - Mobile */}
              <div className="w-20 sm:hidden">
                <LanguageSwitcher />
              </div>

              <Button asChild variant="outline" size="sm" className="gap-2">
                <Link href="/admin/login">
                  <Building2 className="w-4 h-4" />
                  <span className="hidden sm:inline">{t('adminLogin')}</span>
                  <span className="sm:hidden">Admin</span>
                </Link>
              </Button>
              <Button asChild size="sm" className="gap-2">
                <Link href="/contractor/login">
                  <Users className="w-4 h-4" />
                  <span className="hidden sm:inline">
                    {t('contractorLogin')}
                  </span>
                  <span className="sm:hidden">Contractor</span>
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </header>
      {/* Hero Section */}
      <section className="pt-24 pb-16 lg:pb-24 relative overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 z-0">
          <Image
            src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80"
            alt="Modern elevator and building interior"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-white/85 backdrop-blur-[2px]" />
        </div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            {/* Animated Hero Content */}
            <div className="animate-in slide-in-from-bottom-4 duration-1000 mt-8">
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-slate-700 via-blue-600 to-slate-600 bg-clip-text text-transparent mb-6">
                {t('title')}
              </h1>
              <p className="text-xl md:text-2xl text-slate-700 mb-8 max-w-2xl mx-auto">
                {t('subtitle')}
              </p>
            </div>

            {/* Scroll Indicator */}
            <div className="animate-in fade-in duration-1000 delay-700">
              <div className="flex flex-col items-center text-slate-500">
                <p className="text-sm mb-2">{t('scrollToLearn')}</p>
                <ChevronDown className="w-5 h-5 animate-bounce" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* What is SimPLE? Section */}
      <section id="what-is-simple" className="py-16 lg:py-24 bg-slate-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold text-slate-900 mb-6">
              {t('aboutSimple.title')}
            </h2>
            <p className="text-2xl text-blue-600 font-semibold mb-8">
              {t('aboutSimple.subtitle')}
            </p>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed">
              {t('aboutSimple.description')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Digital Transformation */}
            <div className="animate-in slide-in-from-bottom-4 duration-1000 delay-200">
              <div className="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mb-6 mx-auto">
                  <Settings className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-4">
                  {t('aboutSimple.features.digital.title')}
                </h3>
                <p className="text-slate-600">
                  {t('aboutSimple.features.digital.description')}
                </p>
              </div>
            </div>

            {/* Regulatory Compliance */}
            <div className="animate-in slide-in-from-bottom-4 duration-1000 delay-400">
              <div className="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 text-center">
                <div className="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mb-6 mx-auto">
                  <Shield className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-4">
                  {t('aboutSimple.features.compliance.title')}
                </h3>
                <p className="text-slate-600">
                  {t('aboutSimple.features.compliance.description')}
                </p>
              </div>
            </div>

            {/* Operational Efficiency */}
            <div className="animate-in slide-in-from-bottom-4 duration-1000 delay-600">
              <div className="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 text-center">
                <div className="w-16 h-16 bg-yellow-100 rounded-xl flex items-center justify-center mb-6 mx-auto">
                  <Zap className="w-8 h-8 text-yellow-600" />
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-4">
                  {t('aboutSimple.features.efficiency.title')}
                </h3>
                <p className="text-slate-600">
                  {t('aboutSimple.features.efficiency.description')}
                </p>
              </div>
            </div>

            {/* Complete Transparency */}
            <div className="animate-in slide-in-from-bottom-4 duration-1000 delay-800">
              <div className="bg-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mb-6 mx-auto">
                  <Users className="w-8 h-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-4">
                  {t('aboutSimple.features.transparency.title')}
                </h3>
                <p className="text-slate-600">
                  {t('aboutSimple.features.transparency.description')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Hero Image Section */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-6">
          <div className="animate-in slide-in-from-bottom-8 duration-1000 delay-700">
            <div className="relative h-[400px] lg:h-[600px] rounded-3xl overflow-hidden">
              <Image
                src="https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                alt="Professional office building and construction site"
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />
              <div className="absolute bottom-8 left-8 right-8 text-white">
                <h2 className="text-3xl lg:text-5xl font-bold mb-4">
                  {t('heroTitle')}
                </h2>
                <p className="text-lg lg:text-xl opacity-90 max-w-2xl">
                  {t('heroSubtitle')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section with Sliding Animation */}
      <section className="py-16 lg:py-24 bg-slate-50/70 backdrop-blur-sm">
        <div className="container mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
            {/* Admin Portal Info */}
            <div className="animate-in slide-in-from-left-8 duration-1000 delay-300">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-8 rounded-2xl border border-blue-200">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center">
                    <Building2 className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-blue-900">
                      {t('adminPortalTitle')}
                    </h3>
                    <p className="text-blue-700">
                      {t('adminPortalDescription')}
                    </p>
                  </div>
                </div>
                <div className="space-y-3 mb-6">
                  <div className="flex items-center gap-3 text-blue-800">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span>{t('adminFeatures.projectManagement')}</span>
                  </div>
                  <div className="flex items-center gap-3 text-blue-800">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span>{t('adminFeatures.userManagement')}</span>
                  </div>
                  <div className="flex items-center gap-3 text-blue-800">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span>{t('adminFeatures.reports')}</span>
                  </div>
                </div>
                <div className="flex gap-3">
                  <Button
                    asChild
                    className="flex-1 bg-blue-600 hover:bg-blue-700"
                  >
                    <Link href="/admin/login">{t('adminLogin')}</Link>
                  </Button>
                  <Button
                    asChild
                    variant="outline"
                    className="flex-1 border-blue-300 text-blue-700 hover:bg-blue-50"
                  >
                    <Link href="/admin/register">{t('adminRegister')}</Link>
                  </Button>
                </div>
              </div>
            </div>

            {/* Contractor Portal Info */}
            <div className="animate-in slide-in-from-right-8 duration-1000 delay-500">
              <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-8 rounded-2xl border border-slate-200">
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-16 h-16 bg-slate-600 rounded-xl flex items-center justify-center">
                    <Users className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-slate-900">
                      {t('contractorPortalTitle')}
                    </h3>
                    <p className="text-slate-700">
                      {t('contractorPortalDescription')}
                    </p>
                  </div>
                </div>
                <div className="space-y-3 mb-6">
                  <div className="flex items-center gap-3 text-slate-800">
                    <div className="w-2 h-2 bg-slate-600 rounded-full"></div>
                    <span>{t('contractorFeatures.bidding')}</span>
                  </div>
                  <div className="flex items-center gap-3 text-slate-800">
                    <div className="w-2 h-2 bg-slate-600 rounded-full"></div>
                    <span>{t('contractorFeatures.tracking')}</span>
                  </div>
                  <div className="flex items-center gap-3 text-slate-800">
                    <div className="w-2 h-2 bg-slate-600 rounded-full"></div>
                    <span>{t('contractorFeatures.compliance')}</span>
                  </div>
                </div>
                <div className="flex gap-3">
                  <Button
                    asChild
                    className="flex-1 bg-slate-600 hover:bg-slate-700"
                  >
                    <Link href="/contractor/login">{t('contractorLogin')}</Link>
                  </Button>
                  <Button
                    asChild
                    variant="outline"
                    className="flex-1 border-slate-300 text-slate-700 hover:bg-slate-50"
                  >
                    <Link href="/contractor/register">
                      {t('contractorRegister')}
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Product Services Section */}
      <section id="about" className="py-16 lg:py-24 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold text-slate-900 mb-6">
              {t('services.title')}
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              {t('services.subtitle')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Lift Management */}
            <div className="animate-in slide-in-from-bottom-4 duration-1000 delay-200">
              <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mb-6">
                  <Zap className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-2xl font-bold text-slate-900 mb-4">
                  {t('services.liftManagement.title')}
                </h3>
                <p className="text-slate-600 mb-6">
                  {t('services.liftManagement.description')}
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center gap-3 text-slate-700">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span>
                      {t('services.liftManagement.features.maintenance')}
                    </span>
                  </li>
                  <li className="flex items-center gap-3 text-slate-700">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span>
                      {t('services.liftManagement.features.inspection')}
                    </span>
                  </li>
                  <li className="flex items-center gap-3 text-slate-700">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span>
                      {t('services.liftManagement.features.compliance')}
                    </span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Safety & Compliance */}
            <div className="animate-in slide-in-from-bottom-4 duration-1000 delay-400">
              <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                <div className="w-16 h-16 bg-slate-100 rounded-xl flex items-center justify-center mb-6">
                  <Shield className="w-8 h-8 text-slate-600" />
                </div>
                <h3 className="text-2xl font-bold text-slate-900 mb-4">
                  {t('services.safety.title')}
                </h3>
                <p className="text-slate-600 mb-6">
                  {t('services.safety.description')}
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center gap-3 text-slate-700">
                    <div className="w-2 h-2 bg-slate-600 rounded-full"></div>
                    <span>{t('services.safety.features.standards')}</span>
                  </li>
                  <li className="flex items-center gap-3 text-slate-700">
                    <div className="w-2 h-2 bg-slate-600 rounded-full"></div>
                    <span>{t('services.safety.features.certification')}</span>
                  </li>
                  <li className="flex items-center gap-3 text-slate-700">
                    <div className="w-2 h-2 bg-slate-600 rounded-full"></div>
                    <span>{t('services.safety.features.reporting')}</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Digital Platform */}
            <div className="animate-in slide-in-from-bottom-4 duration-1000 delay-600">
              <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mb-6">
                  <Settings className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-2xl font-bold text-slate-900 mb-4">
                  {t('services.platform.title')}
                </h3>
                <p className="text-slate-600 mb-6">
                  {t('services.platform.description')}
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center gap-3 text-slate-700">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span>{t('services.platform.features.realtime')}</span>
                  </li>
                  <li className="flex items-center gap-3 text-slate-700">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span>{t('services.platform.features.analytics')}</span>
                  </li>
                  <li className="flex items-center gap-3 text-slate-700">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <span>{t('services.platform.features.integration')}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Blog Section */}
      <section className="py-16 lg:py-24 bg-slate-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-5xl font-bold text-slate-900 mb-6">
              {t('blog.title')}
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              {t('blog.subtitle')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Blog Post 1 */}
            <div className="animate-in slide-in-from-left-4 duration-1000 delay-200">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="h-48 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                  <BookOpen className="w-16 h-16 text-white" />
                </div>
                <div className="p-6">
                  <div className="flex items-center gap-2 text-blue-600 text-sm mb-3">
                    <Calendar className="w-4 h-4" />
                    <span>{t('blog.post1.date')}</span>
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-3">
                    {t('blog.post1.title')}
                  </h3>
                  <p className="text-slate-600 mb-4">
                    {t('blog.post1.excerpt')}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-2 border-blue-300 text-blue-700 hover:bg-blue-50"
                  >
                    {t('blog.readMore')}
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Blog Post 2 */}
            <div className="animate-in slide-in-from-bottom-4 duration-1000 delay-400">
              <div className="bg-gradient-to-br from-slate-50 to-slate-100 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="h-48 bg-gradient-to-br from-slate-500 to-slate-600 flex items-center justify-center">
                  <Shield className="w-16 h-16 text-white" />
                </div>
                <div className="p-6">
                  <div className="flex items-center gap-2 text-slate-600 text-sm mb-3">
                    <Calendar className="w-4 h-4" />
                    <span>{t('blog.post2.date')}</span>
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-3">
                    {t('blog.post2.title')}
                  </h3>
                  <p className="text-slate-600 mb-4">
                    {t('blog.post2.excerpt')}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-2 border-slate-300 text-slate-700 hover:bg-slate-50"
                  >
                    {t('blog.readMore')}
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Blog Post 3 */}
            <div className="animate-in slide-in-from-right-4 duration-1000 delay-600">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="h-48 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                  <Zap className="w-16 h-16 text-white" />
                </div>
                <div className="p-6">
                  <div className="flex items-center gap-2 text-blue-600 text-sm mb-3">
                    <Calendar className="w-4 h-4" />
                    <span>{t('blog.post3.date')}</span>
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 mb-3">
                    {t('blog.post3.title')}
                  </h3>
                  <p className="text-slate-600 mb-4">
                    {t('blog.post3.excerpt')}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-2 border-blue-300 text-blue-700 hover:bg-blue-50"
                  >
                    {t('blog.readMore')}
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <Button
              asChild
              size="lg"
              variant="outline"
              className="gap-2 border-slate-300 text-slate-700 hover:bg-slate-50"
            >
              <Link href="/blog">
                {t('blog.viewAll')}
                <ArrowRight className="w-4 h-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
      {/* Newsletter Section */}
      <section
        id="contact"
        className="py-16 lg:py-24 bg-gradient-to-r from-slate-600 to-blue-600"
      >
        <div className="container mx-auto px-6">
          <div className="text-center text-white max-w-4xl mx-auto">
            <div className="animate-in slide-in-from-bottom-4 duration-1000">
              <h2 className="text-3xl lg:text-5xl font-bold mb-6">
                {t('newsletter.title')}
              </h2>
              <p className="text-xl mb-8 opacity-90">
                {t('newsletter.subtitle')}
              </p>

              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 max-w-2xl mx-auto">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <input
                      type="email"
                      placeholder={t('newsletter.emailPlaceholder')}
                      className="w-full px-6 py-4 rounded-xl text-slate-900 placeholder-slate-500 border-0 focus:ring-2 focus:ring-white focus:outline-none"
                    />
                  </div>
                  <Button
                    size="lg"
                    className="bg-white text-blue-600 hover:bg-gray-100 px-8 gap-2"
                  >
                    <Send className="w-5 h-5" />
                    {t('newsletter.subscribe')}
                  </Button>
                </div>
                <p className="text-sm opacity-80 mt-4">
                  {t('newsletter.privacy')}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 bg-slate-900 text-white">
        <div className="container mx-auto px-6 text-center">
          <p className="text-slate-400">{t('helpText')}</p>
        </div>
      </footer>
    </div>
  );
}
