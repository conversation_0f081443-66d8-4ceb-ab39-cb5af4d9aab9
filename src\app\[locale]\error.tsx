'use client';

import { useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { AlertTriangle, RefreshCw, ArrowLeft } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

/**
 * Locale-specific error page component for Next.js App Router
 * Handles unexpected errors within locale routes and provides user-friendly error recovery
 */
export default function LocaleErrorPage({ error, reset }: ErrorPageProps) {
  const t = useTranslations('error');

  useEffect(() => {
    // Log error details for debugging
    console.error('Locale-specific error boundary triggered:', error);
  }, [error]);

  const handleRetry = () => {
    try {
      reset();
    } catch (retryError) {
      console.error('Error during retry:', retryError);
      // Fallback: reload the page if reset fails
      window.location.reload();
    }
  };

  // Fallback content in case i18n fails
  const fallbackContent = {
    title: 'Something went wrong',
    description:
      'We encountered an unexpected error. Please try again or go back to the previous page.',
    retryButton: 'Try again',
    goBackButton: 'Go back',
  };

  const getTranslation = (key: keyof typeof fallbackContent) => {
    try {
      return t(key);
    } catch {
      return fallbackContent[key];
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50">
      <Card className="w-full max-w-md mx-auto shadow-lg">
        <CardHeader className="text-center pb-4">
          <div className="flex justify-center mb-4">
            <div className="rounded-full bg-red-100 p-3">
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </div>
          <h1 className="text-2xl font-semibold text-gray-900">
            {getTranslation('title')}
          </h1>
        </CardHeader>

        <CardContent className="text-center space-y-6">
          <p className="text-gray-600 leading-relaxed">
            {getTranslation('description')}
          </p>

          <div className="space-y-3">
            <Button onClick={handleRetry} className="w-full" size="lg">
              <RefreshCw className="mr-2 h-4 w-4" />
              {getTranslation('retryButton')}
            </Button>

            <Button
              variant="outline"
              onClick={() => window.history.back()}
              className="w-full"
              size="lg"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              {getTranslation('goBackButton')}
            </Button>
          </div>

          {process.env.NODE_ENV === 'development' && error.digest && (
            <details className="mt-6 p-4 bg-gray-100 rounded-lg text-left">
              <summary className="cursor-pointer text-sm font-medium text-gray-700 mb-2">
                Error Details (Development)
              </summary>
              <code className="text-xs text-gray-600 break-all">
                Digest: {error.digest}
              </code>
              {error.message && (
                <div className="mt-2">
                  <code className="text-xs text-gray-600 break-all">
                    Message: {error.message}
                  </code>
                </div>
              )}
            </details>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
