# SimPLE - Simple Frontend with Supabase Authentication

A modern Next.js application with Supabase authentication and beautiful UI components.

## Features

- 🔐 Secure authentication with Supabase
- ⚡ TanStack Query for state management
- 🎨 Modern UI with Tailwind CSS and Radix UI
- 🛡️ Protected routes with middleware
- 📱 Responsive design

## Quick Start

### Prerequisites

- Node.js 18+
- pnpm (`npm install -g pnpm`)
- Docker

### Setup

```bash
# Clone and install
git clone <your-repo-url>
cd simple-fe
pnpm install

# Setup environment
cp .env.sample .env.local
```

Edit `.env.local`:

```env
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
```

### Start Development

```bash
# Install and start Supabase CLI
brew install supabase/tap/supabase
supabase start

# Start Next.js
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) to view the app.

## CI/CD & Deployment

This project includes automated CI/CD pipelines with database migrations:

- **Staging**: `develop` branch → staging environment
- **Production**: `main` branch → production environment

### Quick Setup

1. Configure GitHub secrets (see [Quick Start Guide](./docs/QUICK_START_CICD.md))
2. Push to `develop` or `main` branches
3. Migrations run automatically before deployment

For detailed setup, see:

- [Quick Start CI/CD Guide](./docs/QUICK_START_CICD.md)
- [Complete CI/CD Setup](./docs/CICD_MIGRATION_SETUP.md)
- [Email Customization Guide](./docs/EMAIL_CUSTOMIZATION_GUIDE.md)

## Email Customization

SimPLE includes comprehensive email customization with:

- **Custom HTML templates** for all authentication emails
- **Bilingual support** (English and Malay)
- **Consistent branding** and professional styling
- **Automatic deployment** with Supabase's built-in email service

See [Email Customization Guide](./docs/EMAIL_CUSTOMIZATION_GUIDE.md) for full details.

## Development Workflow (IMPORTANT)

**Before making any changes, read this section carefully!**

### Pre-Push Validation (Mandatory)

Always run this command before pushing:

```bash
pnpm validate:full
```

This will:

- ✅ Check TypeScript types
- ✅ Run ESLint
- ✅ Build the project
- ✅ Verify everything works

### Git Hooks Protection

We have automated protection in place:

- **Pre-commit**: Runs linting and type checking
- **Pre-push**: Runs full build validation
- **PR validation**: GitHub Actions check every pull request

### Quick Commands

```bash
# Development
pnpm dev              # Start development server
pnpm build            # Build for production
pnpm type-check       # Check TypeScript types
pnpm lint             # Check code style
pnpm validate         # Quick validation
pnpm validate:full    # Comprehensive validation

# Supabase Local
supabase start         # Start local services
supabase stop          # Stop services
supabase db reset      # Reset database with migrations

# Database Types & Migrations
pnpm run db:types:local         # Generate types from local DB
pnpm run db:types:linked        # Generate types from linked remote DB
pnpm run db:migration:new       # Create new migration
pnpm run db:migration:list      # List local migrations
pnpm run db:push               # Apply migrations to linked project

# CI/CD Validation
./scripts/validate-cicd.sh     # Validate CI/CD setup
```

## Local Services

- **App**: http://localhost:3000
- **Supabase Studio**: http://127.0.0.1:54323
- **Email Testing**: http://127.0.0.1:54324

## Technologies

- Next.js 15 with App Router
- Supabase Auth & Database
- TanStack Query
- Tailwind CSS + Radix UI
- TypeScript

## License

MIT
