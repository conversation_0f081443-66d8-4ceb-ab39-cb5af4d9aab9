'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import {
  ProjectFormData,
  ProjectFormMultiStep,
  useCreateProject,
} from '@/features/projects';
import { Building2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

const CreateProjectPage = () => {
  const router = useRouter();
  const t = useTranslations('pages.projects');

  const createProjectMutation = useCreateProject();

  const handleSubmit = async (data: ProjectFormData) => {
    try {
      console.log('Submitting project data:', data);

      await createProjectMutation.mutateAsync(data);

      // Show success notification
      console.log('Project created successfully!');

      // Redirect back to projects page
      router.push('/projects');
    } catch (error) {
      // Error handling is done in the mutation
      console.error('Error in create page:', error);
    }
  };

  const handleCancel = () => {
    router.push('/projects');
  };

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <div className="space-y-6">
        {/* Breadcrumb */}
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/projects" className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  {t('title')}
                </Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{t('create.title')}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Header
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Button variant="ghost" size="sm" asChild>
                            <Link href="/projects">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                {common('back')}
                            </Link>
                        </Button>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                                {t('create.title')}
                            </h1>
                            <p className="text-gray-500 mt-1">
                                {t('create.description')}
                            </p>
                        </div>
                    </div>
                </div> */}

        {/* Form */}
        <ProjectFormMultiStep
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={createProjectMutation.isPending}
        />

        {/* Development Info */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="text-sm font-medium text-yellow-800 mb-2">
              Development Info
            </h3>
            <div className="text-xs text-yellow-700 space-y-1">
              <p>• Make sure Supabase is running locally</p>
              <p>• Files will be uploaded to OBS storage</p>
              <p>• Check browser console for detailed logs</p>
              <p>
                • Mutation status:{' '}
                {createProjectMutation.isPending ? 'Loading...' : 'Ready'}
              </p>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        .required::after {
          content: ' *';
          color: red;
        }
      `}</style>
    </div>
  );
};

export default CreateProjectPage;
