---
applyTo: '**'
---

Coding standards, domain knowledge, and preferences that AI should follow.

# Copilot Coding Rules for SimPLE Frontend

Welcome to SimPLE!  
To keep this codebase clean, scalable, and easy for all contributors, <PERSON>pilot and all developers must follow these rules and best practices.  
**Always prioritize clarity, maintainability, and modern patterns for Next.js, Supabase, and TypeScript.**

---

## General Principles

- Write idiomatic, modern TypeScript (strict mode friendly).
- Prioritize readability over clever hacks.
- Comment non-obvious code and complex logic.
- Keep pull requests focused and atomic.

---

## Feature-Based Folder Organization

- **All business/domain logic should be grouped under `src/features/[feature-name]/`.**
  - Each feature folder must contain its own components, hooks, types, utils, and schemas as needed.
  - Example structure:
    ```
    src/features/
      contractor-onboarding/
        components/
        hooks/
        types/
        schemas/
        utils/
        index.ts
      dashboard/
        components/
        hooks/
        types/
        index.ts
      auth/
        components/
        hooks/
        types/
        index.ts
    ```
  - **Keep global/shared code only in `/components/ui/`, `/components/rbac/`, `/lib/`, `/types/`, `/hooks/`, `/i18n/`, or `/providers/`.**
  - Use barrel (`index.ts`) files for clean imports at the feature level.
  - Co-locate tests with their respective feature folders where possible.
- **Do not scatter business logic across global folders**—always prefer feature folder grouping for business/domain-specific code.

---

## Component Architecture

- **Split UI into small, reusable components.**
  - Each component should have a single responsibility.
  - Extract child components or hooks if files grow too large (~150 lines).
  - Use folder-based structure (`app/feature-name/`) for related pages, components, hooks, and utils.
  - Use server components by default in `app/`, only use client components when necessary (interactivity, hooks).

---

## UI Components: Use shadcn/ui First

- **Always check if a [shadcn/ui](https://ui.shadcn.com/) component exists before building any custom UI component.**
  - If the component you need is available in shadcn/ui, use it directly or extend it with minimal changes.
  - Do **not** duplicate or rebuild UI patterns already provided by shadcn/ui.
  - Only build custom components if shadcn/ui does not provide the needed functionality or requires heavy customization.
- **Consistency:** Use shadcn/ui for all common UI (buttons, dialogs, cards, dropdowns, inputs, toasts, alerts, etc.) for a consistent look and feel.
- **Accessibility:** Follow shadcn/ui accessibility and styling recommendations.
- **Documentation:** If a custom UI component is necessary, document the reason in code comments and PRs.

---

## Internationalization (i18n) – Malay & English Support

- **All user-facing text must support both Malay and English.**
  - Use a dedicated i18n library such as [next-intl](https://github.com/amannn/next-intl) or [react-i18next](https://react.i18next.com/) to manage translations and locale switching.
  - **Never hardcode strings** in components—always use translation keys.
  - Add/maintain translations for both `ms` (Malay) and `en` (English) in the respective translation files.
  - For new features/pages/components, always provide keys and translation values for both languages in PRs.
  - When writing UI copy, ensure clarity and accuracy in both languages; ask for review from a native Malay speaker if possible.
  - Use sensible fallback (default to English if Malay translation is missing).

**Example usage**

```tsx
import { useTranslations } from 'next-intl'; // or your chosen library

export default function WelcomeMessage() {
  const t = useTranslations();
  return <h1>{t('welcome')}</h1>; // 'welcome' key must exist in both en and ms translation files
}

---

## State & Data Management

- Use **TanStack Query** for data fetching, caching, and server state.
  Never fetch data directly in components—always use hooks (e.g., `useProfile()`).
- Use React’s `useState` or context for local UI state only.
- Never put business/data logic directly in the UI layer—extract to hooks, helpers, or service files.

---

## Styling

- Use **Tailwind CSS utility classes** for styling—avoid custom CSS unless necessary.
- Use **Radix UI primitives** for accessible, consistent foundations.

---

## Authentication & Authorization

- Use **Supabase Auth** hooks for session and user data.
- Protect sensitive routes using Next.js middleware—don’t duplicate RBAC logic in UI components.
- Store role/onboarding status in cookies where possible to minimize DB calls in middleware.

---

## Code Quality

- **Type everything!** Avoid `any` and unnecessary type assertions.
- Lint and format code before committing (`pnpm lint` and `pnpm format`).
- Write unit tests for critical logic and utility functions.
- Prefer async/await, avoid callbacks or Promises in component bodies.

---

## Patterns & Anti-patterns

- Prefer composition (combining components and hooks) over inheritance or copy-paste.
- Avoid prop drilling—use context or custom hooks if data needs to be shared deeply.
- Never put business logic or fetches in UI components; always extract to hooks/services.

---

## Naming & Organization

- Use clear, descriptive names for files, variables, and functions.
- Use **kebab-case** for folder and file names, **camelCase** for variables and functions, **PascalCase** for components and types.

---

## Documentation

- Use JSDoc to document exported functions, hooks, and components.
- Update the README or internal docs for any new patterns or architectural decisions.
- Document in code or PRs if a convention needs to be broken and why.

---

> **Summary:**
> Keep code clean, modular, well-typed, and consistent.
> Always use shadcn/ui before building custom UI.
> Extract logic, split components, and follow modern patterns.
```
