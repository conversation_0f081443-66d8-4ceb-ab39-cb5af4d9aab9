'use client';

import { createContext, ReactNode, useContext, useState } from 'react';

interface ProjectContextValue {
  selectedProjectId: string | null;
  selectProject: (projectId: string) => void;
  clearProject: () => void;
  isInProjectContext: boolean;
}

const ProjectContext = createContext<ProjectContextValue | undefined>(
  undefined,
);

interface ProjectContextProviderProps {
  children: ReactNode;
}

export function ProjectContextProvider({
  children,
}: ProjectContextProviderProps) {
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(
    null,
  );

  const selectProject = (projectId: string) => {
    setSelectedProjectId(projectId);
  };

  const clearProject = () => {
    setSelectedProjectId(null);
  };

  const isInProjectContext = selectedProjectId !== null;

  return (
    <ProjectContext.Provider
      value={{
        selectedProjectId,
        selectProject,
        clearProject,
        isInProjectContext,
      }}
    >
      {children}
    </ProjectContext.Provider>
  );
}

export function useProjectContext() {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error(
      'useProjectContext must be used within a ProjectContextProvider',
    );
  }
  return context;
}
