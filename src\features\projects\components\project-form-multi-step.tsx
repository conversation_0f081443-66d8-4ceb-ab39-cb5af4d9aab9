'use client';

import { UnsavedChangesDialog } from '@/components/ui/unsaved-changes-dialog';
import { useUnsavedChanges } from '@/hooks/use-unsaved-changes';
import { Building2 } from 'lucide-react';
import { useState } from 'react';
import {
  type PmasSchema,
  type ProjectDetailsSchema,
} from '../schemas/project-schema';
import { type ProjectFormData, type ProjectFormProps } from '../types/project';
import { PmasStep, usePmasForm } from './pmas-step';
import {
  ProjectDetailsStep,
  useProjectDetailsForm,
} from './project-details-step';
import { ProjectFormProgress } from './project-form-progress';

enum FormStep {
  PROJECT_DETAILS = 1,
  PMA = 2,
}

/**
 * Multi-step project creation form
 * Orchestrates the two form steps: Project Details and PMA
 */
export function ProjectFormMultiStep({
  initialData,
  onSubmit,
  onCancel,
  isLoading,
}: ProjectFormProps) {
  const [currentStep, setCurrentStep] = useState(FormStep.PROJECT_DETAILS);
  const [formData, setFormData] = useState<Partial<ProjectFormData>>(
    initialData || {},
  );

  // Initialize form instances for each step
  const projectDetailsForm = useProjectDetailsForm({
    ...formData,
    status: formData.status as
      | 'pending'
      | 'active'
      | 'completed'
      | 'cancelled'
      | undefined,
    state: formData.state as
      | 'JH'
      | 'KD'
      | 'KT'
      | 'ML'
      | 'NS'
      | 'PH'
      | 'PN'
      | 'PK'
      | 'PL'
      | 'SB'
      | 'SW'
      | 'SL'
      | 'TR'
      | 'WP'
      | 'LBN'
      | 'PW'
      | 'OTH'
      | undefined,
  });
  const pmasForm = usePmasForm(formData.pmas);

  // Check if any form has unsaved changes
  const hasUnsavedChanges =
    projectDetailsForm.formState.isDirty || pmasForm.formState.isDirty;

  // Unsaved changes warning
  const { showConfirmDialog, confirmNavigation, cancelNavigation, markSaved } =
    useUnsavedChanges({
      hasUnsavedChanges,
      message:
        'You have unsaved project details. Are you sure you want to leave this page?',
      onNavigateAway: () => {
        console.log('User navigated away with unsaved changes');
      },
    });

  const handleNext = (stepData: ProjectDetailsSchema) => {
    console.log('handleNext called with step:', currentStep, 'data:', stepData);
    switch (currentStep) {
      case FormStep.PROJECT_DETAILS:
        setFormData((prev) => ({
          ...prev,
          ...(stepData as ProjectDetailsSchema),
        }));
        setCurrentStep(FormStep.PMA);
        break;
    }
  };

  const handlePrevious = () => {
    if (currentStep > FormStep.PROJECT_DETAILS) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleFinalSubmit = async (pmasData: PmasSchema) => {
    const completeData: ProjectFormData = {
      ...formData,
      pmas: pmasData,
    } as ProjectFormData;

    // Mark changes as saved before submitting
    markSaved();
    await onSubmit(completeData);
  };

  // Enhanced cancel handler that checks for unsaved changes
  const handleCancel = () => {
    if (hasUnsavedChanges) {
      if (
        window.confirm(
          'You have unsaved changes. Are you sure you want to cancel?',
        )
      ) {
        markSaved();
        onCancel?.();
      }
    } else {
      onCancel?.();
    }
  };

  return (
    <div className="bg-card rounded-3xl shadow-lg p-8 lg:p-12 border max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-foreground mb-2 flex items-center gap-3">
          <Building2 className="h-6 w-6 text-primary" />
          Create New Project
        </h1>
        <p className="text-muted-foreground">
          Set up your project with all necessary details and documentation
        </p>
      </div>

      {/* Progress Indicator */}
      <ProjectFormProgress currentStep={currentStep} />

      {/* Current Step Content */}
      <div className="space-y-8">
        {currentStep === FormStep.PROJECT_DETAILS && (
          <ProjectDetailsStep
            form={projectDetailsForm}
            onNext={handleNext}
            onCancel={handleCancel}
            isLoading={isLoading}
          />
        )}

        {currentStep === FormStep.PMA && (
          <PmasStep
            form={pmasForm}
            onSubmit={handleFinalSubmit}
            onPrevious={handlePrevious}
            onCancel={handleCancel}
            isLoading={isLoading}
          />
        )}
      </div>

      {/* Footer */}
      <div className="mt-8 pt-6 border-t border-border">
        <p className="text-sm text-muted-foreground text-center">
          All fields marked with <span className="text-destructive">*</span> are
          required
        </p>
      </div>

      {/* Unsaved Changes Dialog */}
      <UnsavedChangesDialog
        open={showConfirmDialog}
        onConfirm={confirmNavigation}
        onCancel={cancelNavigation}
        description="You have unsaved changes that will be lost if you leave this page. Are you sure you want to continue?"
      />
    </div>
  );
}
