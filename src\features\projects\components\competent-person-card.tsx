'use client';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileUploadDropzone } from '@/components/ui/file-upload-dropzone';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Mail, Phone, Trash2, Upload, User } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { type CompetentPersonsSchema } from '../schemas/project-schema';

interface CompetentPersonCardProps {
  form: UseFormReturn<CompetentPersonsSchema>;
  index: number;
  onRemove: () => void;
  canRemove: boolean;
}

/**
 * Individual competent person form card
 * Handles the form fields for a single competent person entry
 */
export function CompetentPersonCard({
  form,
  index,
  onRemove,
  canRemove,
}: CompetentPersonCardProps) {
  const fieldPrefix = `competent_persons.${index}` as const;

  return (
    <Card className="relative">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">
            Competent Person {index + 1}
          </CardTitle>
          {canRemove && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={onRemove}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Remove
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Contact Information */}
        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label
              htmlFor={`cp_name_${index}`}
              className="required flex items-center gap-2"
            >
              <User className="h-4 w-4" />
              Name <span className="text-destructive">*</span>
            </Label>
            <Input
              id={`cp_name_${index}`}
              placeholder="Enter competent person name"
              {...form.register(`${fieldPrefix}.name`)}
            />
            {form.formState.errors.competent_persons?.[index]?.name && (
              <p className="text-sm text-destructive">
                {form.formState.errors.competent_persons[index]?.name?.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label
              htmlFor={`cp_phone_${index}`}
              className="required flex items-center gap-2"
            >
              <Phone className="h-4 w-4" />
              Phone Number <span className="text-destructive">*</span>
            </Label>
            <Input
              id={`cp_phone_${index}`}
              placeholder="e.g., ************"
              {...form.register(`${fieldPrefix}.phone_number`)}
            />
            {form.formState.errors.competent_persons?.[index]?.phone_number && (
              <p className="text-sm text-destructive">
                {
                  form.formState.errors.competent_persons[index]?.phone_number
                    ?.message
                }
              </p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label
            htmlFor={`cp_email_${index}`}
            className="required flex items-center gap-2"
          >
            <Mail className="h-4 w-4" />
            Email Address <span className="text-destructive">*</span>
          </Label>
          <Input
            id={`cp_email_${index}`}
            type="email"
            placeholder="<EMAIL>"
            {...form.register(`${fieldPrefix}.email`)}
          />
          {form.formState.errors.competent_persons?.[index]?.email && (
            <p className="text-sm text-destructive">
              {form.formState.errors.competent_persons[index]?.email?.message}
            </p>
          )}
        </div>

        {/* File Uploads */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              Registration Certificate (Optional)
            </Label>
            <FileUploadDropzone
              onFilesChange={(files) => {
                const file = files[0] || undefined;
                form.setValue(`${fieldPrefix}.registration_cert_file`, file);
                if (file) {
                  form.trigger(`${fieldPrefix}.registration_cert_file`);
                }
              }}
              accept=".pdf,.jpg,.jpeg,.png"
              maxSize={5 * 1024 * 1024} // 5MB
              maxFiles={1}
              files={(() => {
                const file = form.watch(
                  `${fieldPrefix}.registration_cert_file`,
                );
                return file ? [file] : [];
              })()}
            />
            {form.formState.errors.competent_persons?.[index]
              ?.registration_cert_file && (
              <p className="text-sm text-destructive">
                {
                  form.formState.errors.competent_persons[index]
                    ?.registration_cert_file?.message
                }
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              LIF List Files (Optional)
            </Label>
            <FileUploadDropzone
              onFilesChange={(files) => {
                form.setValue(
                  `${fieldPrefix}.lif_list_files`,
                  files.length > 0 ? files : undefined,
                );
                if (files.length > 0) {
                  form.trigger(`${fieldPrefix}.lif_list_files`);
                }
              }}
              accept=".pdf,.jpg,.jpeg,.png"
              maxSize={5 * 1024 * 1024} // 5MB
              maxFiles={5}
              files={form.watch(`${fieldPrefix}.lif_list_files`) || []}
            />
            {form.formState.errors.competent_persons?.[index]
              ?.lif_list_files && (
              <p className="text-sm text-destructive">
                {
                  form.formState.errors.competent_persons[index]?.lif_list_files
                    ?.message
                }
              </p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
