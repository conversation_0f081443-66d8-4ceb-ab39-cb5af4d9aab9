'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useProject } from '@/features/projects';
import { useProjectContext } from '@/providers/project-context';
import { Users } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function MembersPage() {
  const { selectedProjectId, isInProjectContext } = useProjectContext();
  const { data: project, isLoading } = useProject(selectedProjectId || '');
  const router = useRouter();

  useEffect(() => {
    if (!isInProjectContext) {
      router.push('/projects');
    }
  }, [isInProjectContext, router]);

  if (!isInProjectContext) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading project members...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <div className="p-3 rounded-xl bg-primary/10">
            <Users className="h-8 w-8 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-900">
              Project Members
            </h1>
            <p className="text-gray-500 mt-1">
              {project
                ? `Team members for ${project.name}`
                : 'Manage project team'}
            </p>
          </div>
        </div>

        {/* Content */}
        <Card>
          <CardHeader>
            <CardTitle>Team Members</CardTitle>
            <CardDescription>
              Manage project team members and their roles
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-muted-foreground">
              <Users className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p>Project members will be displayed here.</p>
              <p className="text-sm mt-2">This feature is under development.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
