'use client';

import { cn } from '@/lib/utils';
import { AlertCircle, CheckCircle, FileText, Upload, X } from 'lucide-react';
import * as React from 'react';
import { Button } from './button';

interface FileUploadDropzoneProps {
  onFilesChange: (files: File[]) => void;
  accept?: string;
  maxSize?: number; // in bytes
  maxFiles?: number;
  disabled?: boolean;
  className?: string;
  files?: File[]; // Controlled files prop
}

export function FileUploadDropzone({
  onFilesChange,
  accept = '.pdf,.jpg,.jpeg,.png',
  maxSize = 5 * 1024 * 1024, // 5MB
  maxFiles = 1,
  disabled = false,
  className,
  files, // Controlled files prop
}: FileUploadDropzoneProps) {
  const [dragActive, setDragActive] = React.useState(false);
  const [errors, setErrors] = React.useState<string[]>([]);
  const inputRef = React.useRef<HTMLInputElement>(null);

  const handleDrag = React.useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleFiles = React.useCallback(
    (newFiles: FileList | File[]) => {
      const validateFile = (file: File): string | null => {
        if (file.size > maxSize) {
          return `File "${file.name}" is too large. Maximum size is ${Math.round(maxSize / 1024 / 1024)}MB.`;
        }

        const acceptedTypes = accept.split(',').map((type) => type.trim());
        const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
        const mimeType = file.type;

        const isValidType = acceptedTypes.some((type) => {
          if (type.startsWith('.')) {
            return fileExtension === type;
          }
          return mimeType.includes(type.replace('*', ''));
        });

        if (!isValidType) {
          return `File "${file.name}" has an invalid type. Accepted types: ${accept}`;
        }

        return null;
      };

      const fileArray = Array.from(newFiles);
      const newErrors: string[] = [];

      // Validate each file
      const validFiles: File[] = [];
      fileArray.forEach((file) => {
        const error = validateFile(file);
        if (error) {
          newErrors.push(error);
        } else {
          validFiles.push(file);
        }
      });

      // Check total file count - use controlled files length if available
      const currentFileCount = files ? files.length : 0;
      const totalFiles = currentFileCount + validFiles.length;
      if (totalFiles > maxFiles) {
        newErrors.push(`Maximum ${maxFiles} file(s) allowed.`);
        const allowedCount = maxFiles - currentFileCount;
        validFiles.splice(allowedCount);
      }

      setErrors(newErrors);

      if (validFiles.length > 0) {
        // In controlled mode, replace files if single file, otherwise append
        const finalFiles =
          maxFiles === 1 ? validFiles : [...(files || []), ...validFiles];
        onFilesChange(finalFiles);
      }

      // Clear errors if no new files and no existing errors
      if (validFiles.length === 0 && newErrors.length === 0) {
        setErrors([]);
      }
    },
    [files, maxFiles, onFilesChange, accept, maxSize],
  );

  const handleDrop = React.useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);

      if (disabled) return;

      const droppedFiles = e.dataTransfer.files;
      if (droppedFiles && droppedFiles.length > 0) {
        handleFiles(droppedFiles);
      }
    },
    [handleFiles, disabled],
  );

  const handleInputChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      e.preventDefault();
      if (disabled) return;

      const selectedFiles = e.target.files;
      if (selectedFiles && selectedFiles.length > 0) {
        handleFiles(selectedFiles);
      }

      // Reset the input value to allow selecting the same file again
      if (e.target) {
        e.target.value = '';
      }
    },
    [handleFiles, disabled],
  );

  const openFileDialog = React.useCallback(
    (e?: React.MouseEvent) => {
      if (disabled) return;
      e?.preventDefault();
      e?.stopPropagation();
      inputRef.current?.click();
    },
    [disabled],
  );

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const removeFile = (index: number) => {
    if (!files) return;
    const updatedFiles = files.filter((_, i) => i !== index);
    onFilesChange(updatedFiles);
  };

  return (
    <div className={cn('w-full space-y-3', className)}>
      {/* Drop Zone */}
      <div
        className={cn(
          'relative border-2 border-dashed rounded-lg p-6 transition-all duration-200 group',
          dragActive
            ? 'border-primary bg-primary/5 scale-[1.01]'
            : 'border-border hover:border-primary/60 hover:bg-muted/30',
          disabled && 'opacity-50 cursor-not-allowed',
          !disabled && 'cursor-pointer',
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        <input
          ref={inputRef}
          type="file"
          multiple={maxFiles > 1}
          accept={accept}
          onChange={handleInputChange}
          disabled={disabled}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          onClick={(e) => e.stopPropagation()}
        />

        <div className="flex flex-col items-center justify-center space-y-3 min-h-[80px]">
          <div
            className={cn(
              'p-3 rounded-full transition-all duration-200',
              dragActive ? 'bg-primary/15' : 'bg-primary/10',
            )}
          >
            <Upload
              className={cn(
                'w-5 h-5 transition-all duration-200',
                dragActive
                  ? 'text-primary'
                  : 'text-primary/70 group-hover:text-primary',
              )}
            />
          </div>

          <div className="text-center space-y-1">
            <p className="text-sm font-medium text-foreground">
              Drop {maxFiles === 1 ? 'file' : 'files'} here or{' '}
              <span className="text-primary underline underline-offset-2">
                browse
              </span>
            </p>
            <p className="text-xs text-muted-foreground">
              {accept.replace(/\./g, '').toUpperCase()} • Max{' '}
              {Math.round(maxSize / 1024 / 1024)}MB
              {maxFiles > 1 && ` • Up to ${maxFiles} files`}
            </p>
          </div>
        </div>
      </div>

      {/* File List */}
      {files && files.length > 0 && (
        <div className="space-y-2">
          <p className="text-sm font-medium text-foreground">
            {files.length === 1
              ? 'Selected File:'
              : `Selected Files (${files.length}):`}
          </p>
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {files.map((file, index) => (
              <div
                key={`${file.name}-${index}`}
                className="flex items-center justify-between p-2 bg-muted/30 rounded-md border group hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center space-x-2 min-w-0 flex-1">
                  <div className="flex-shrink-0">
                    <FileText className="w-4 h-4 text-primary" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p
                      className="text-sm font-medium truncate"
                      title={file.name}
                    >
                      {file.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 flex-shrink-0">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeFile(index);
                    }}
                    className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-destructive/10"
                  >
                    <X className="w-3 h-3 text-muted-foreground hover:text-destructive" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="space-y-1">
          {errors.map((error, index) => (
            <div
              key={index}
              className="flex items-center space-x-2 text-xs text-destructive bg-destructive/5 p-2 rounded-md border border-destructive/20"
            >
              <AlertCircle className="w-3 h-3 flex-shrink-0" />
              <span>{error}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
