/**
 * Contractor Onboarding Feature
 *
 * This feature handles the multi-step contractor onboarding process including
 * company registration, profile setup, and verification steps.
 */

// Export main components
export { ContractorOnboarding } from './components/contractor-onboarding';
export { ContractorOnboardingProgress } from './components/contractor-onboarding-progress';

// Export step components
export { ContractorOnboardingStep1 } from './components/contractor-onboarding-step1';
export { ContractorOnboardingStep3 } from './components/contractor-onboarding-step3';
export { ContractorOnboardingStep4 } from './components/contractor-onboarding-step4';

// Export company components
export { CompanyForm } from './components/company-form';
export { CompanyRegistrationOption } from './components/company-registration-option';
export { CreateCompanyForm } from './components/create-company-form';
export { JoinCompanyForm } from './components/join-company-form';

// Export hooks
export { useCheckCompanyNameAvailability } from './hooks/use-company-name-availability';
export { useCompleteContractorOnboarding } from './hooks/use-complete-contractor-onboarding';
export { useContractorOnboarding } from './hooks/use-contractor-onboarding';

// Export schemas
export * from './schemas/contractor-onboarding-schemas';
