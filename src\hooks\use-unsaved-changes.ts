'use client';

import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

interface UseUnsavedChangesOptions {
  hasUnsavedChanges: boolean;
  message?: string;
  onNavigateAway?: () => void;
}

interface UseUnsavedChangesReturn {
  showConfirmDialog: boolean;
  setShowConfirmDialog: (show: boolean) => void;
  confirmNavigation: () => void;
  cancelNavigation: () => void;
  markSaved: () => void;
}

/**
 * Hook to warn users when they try to leave a page with unsaved changes.
 * Handles both browser refresh/close and internal navigation.
 *
 * @example
 * ```tsx
 * const { showConfirmDialog, confirmNavigation, cancelNavigation, markSaved } = useUnsavedChanges({
 *   hasUnsavedChanges: form.formState.isDirty,
 *   message: 'You have unsaved changes. Are you sure you want to leave?'
 * });
 * ```
 */
export function useUnsavedChanges({
  hasUnsavedChanges,
  message = 'You have unsaved changes. Are you sure you want to leave?',
  onNavigateAway,
}: UseUnsavedChangesOptions): UseUnsavedChangesReturn {
  const router = useRouter();
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(
    null,
  );
  const [isNavigating, setIsNavigating] = useState(false);

  // Handle browser refresh/close with beforeunload
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges && !isNavigating) {
        e.preventDefault();
        e.returnValue = message;
        return message;
      }
    };

    if (hasUnsavedChanges) {
      window.addEventListener('beforeunload', handleBeforeUnload);
    }

    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges, message, isNavigating]);

  // Override browser back button behavior
  useEffect(() => {
    const handlePopState = (e: PopStateEvent) => {
      if (hasUnsavedChanges && !isNavigating) {
        e.preventDefault();
        setShowConfirmDialog(true);
        // Push the current state back to prevent navigation
        window.history.pushState(null, '', window.location.href);
      }
    };

    if (hasUnsavedChanges) {
      // Add a history entry to catch back button
      window.history.pushState(null, '', window.location.href);
      window.addEventListener('popstate', handlePopState);
    }

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [hasUnsavedChanges, isNavigating]);

  const confirmNavigation = useCallback(() => {
    setIsNavigating(true);
    setShowConfirmDialog(false);

    if (onNavigateAway) {
      onNavigateAway();
    }

    if (pendingNavigation) {
      router.push(pendingNavigation);
      setPendingNavigation(null);
    } else {
      // If no pending navigation, user might have used back button
      window.history.back();
    }

    // Reset after a short delay to allow navigation to complete
    setTimeout(() => setIsNavigating(false), 100);
  }, [router, pendingNavigation, onNavigateAway]);

  const cancelNavigation = useCallback(() => {
    setShowConfirmDialog(false);
    setPendingNavigation(null);
  }, []);

  const markSaved = useCallback(() => {
    setIsNavigating(false);
    setShowConfirmDialog(false);
    setPendingNavigation(null);
  }, []);

  return {
    showConfirmDialog,
    setShowConfirmDialog,
    confirmNavigation,
    cancelNavigation,
    markSaved,
  };
}
