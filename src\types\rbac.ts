import type { UserRole } from './auth';

// Define available permissions/actions for lift management system
export type Permission =
  // Dashboard & Analytics
  | 'dashboard.view'
  | 'analytics.view'
  | 'analytics.manage'

  // Reports
  | 'reports.view'
  | 'reports.create'
  | 'reports.manage'

  // Profile & Settings
  | 'profile.view'
  | 'profile.edit'
  | 'settings.view'
  | 'settings.manage'

  // Project Management
  | 'projects.view'
  | 'projects.create'
  | 'projects.edit'
  | 'projects.delete'
  | 'projects.manage'

  // Project Management
  | 'projects.view'
  | 'projects.create'
  | 'projects.edit'
  | 'projects.delete'
  | 'projects.manage'

  // Lift Management
  | 'lifts.view'
  | 'lifts.create'
  | 'lifts.edit'
  | 'lifts.delete'
  | 'lifts.manage'
  | 'lifts.assign'

  // Building Management
  | 'buildings.view'
  | 'buildings.create'
  | 'buildings.edit'
  | 'buildings.delete'
  | 'buildings.manage'

  // Contractor Management
  | 'contractors.view'
  | 'contractors.create'
  | 'contractors.edit'
  | 'contractors.delete'
  | 'contractors.manage'
  | 'contractors.activate'
  | 'contractors.deactivate'
  | 'contractors.blacklist'
  | 'contractors.approve'

  // Client Management
  | 'clients.view'
  | 'clients.create'
  | 'clients.edit'
  | 'clients.delete'
  | 'clients.manage'

  // Contract Management
  | 'contracts.view'
  | 'contracts.create'
  | 'contracts.edit'
  | 'contracts.delete'
  | 'contracts.manage'
  | 'contracts.approve'

  // Daily Logs
  | 'daily_logs.view'
  | 'daily_logs.create'
  | 'daily_logs.edit'
  | 'daily_logs.delete'
  | 'daily_logs.manage'

  // PMA Management
  | 'pmas.view'
  | 'pmas.create'
  | 'pmas.edit'
  | 'pmas.approve'
  | 'pmas.reject'
  | 'pmas.manage'

  // Complaint Management
  | 'complaints.view'
  | 'complaints.create'
  | 'complaints.edit'
  | 'complaints.resolve'
  | 'complaints.assign'
  | 'complaints.manage'

  // Blacklist Management
  | 'blacklist.view'
  | 'blacklist.create'
  | 'blacklist.edit'
  | 'blacklist.delete'
  | 'blacklist.manage'

  // User Management
  | 'users.view'
  | 'users.create'
  | 'users.edit'
  | 'users.delete'
  | 'users.activate'
  | 'users.deactivate'
  | 'users.manage';

// Define resources that can be protected
export type Resource =
  | 'dashboard'
  | 'lifts'
  | 'buildings'
  | 'contractors'
  | 'clients'
  | 'contracts'
  | 'daily_logs'
  | 'pmas'
  | 'complaints'
  | 'reports'
  | 'analytics'
  | 'profile'
  | 'projects'
  | 'users'
  | 'settings'
  | 'blacklist';

// Define actions that can be performed on resources
export type Action =
  | 'view'
  | 'create'
  | 'edit'
  | 'delete'
  | 'manage'
  | 'approve'
  | 'reject'
  | 'blacklist'
  | 'activate'
  | 'deactivate'
  | 'assign'
  | 'resolve';

// Route permissions type
export type RoutePermission = {
  path: string;
  permission: Permission;
  roles: UserRole[];
};

// Menu item with permission requirements
export type PermissionMenuitem = {
  title: string;
  url: string;
  icon: React.ComponentType<{ className?: string }>;
  permission: Permission;
  roles: UserRole[];
  description?: string;
};

// Permission check result
export type PermissionResult = {
  allowed: boolean;
  reason?: string;
};
