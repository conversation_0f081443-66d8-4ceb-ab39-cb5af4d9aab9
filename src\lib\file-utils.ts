/**
 * File utility functions for upload validation and formatting
 */

/**
 * Format file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Validate file size meets minimum requirement
 */
export function validateFileSize(
  file: File,
  minSizeMB: number = 10,
): { isValid: boolean; error?: string } {
  const minSizeBytes = minSizeMB * 1024 * 1024; // Convert MB to bytes

  if (file.size < minSizeBytes) {
    return {
      isValid: false,
      error: `File "${file.name}" is too small. Minimum size is ${minSizeMB}MB. Current size: ${formatFileSize(file.size)}`,
    };
  }

  return { isValid: true };
}

/**
 * Validate file type is acceptable
 */
export function validateFileType(
  file: File,
  allowedTypes: string[],
): { isValid: boolean; error?: string } {
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
  const mimeType = file.type;

  const isValidType = allowedTypes.some((type) => {
    if (type.startsWith('.')) {
      return fileExtension === type;
    }
    return mimeType.includes(type.replace('*', ''));
  });

  if (!isValidType) {
    return {
      isValid: false,
      error: `File "${file.name}" has an invalid type. Accepted types: ${allowedTypes.join(', ')}`,
    };
  }

  return { isValid: true };
}

/**
 * Comprehensive file validation for contractor uploads
 */
export function validateContractorFile(
  file: File,
  type: 'liftList' | 'certificate',
  minSizeMB: number = 10,
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Define allowed types based on file category
  const allowedTypes =
    type === 'liftList'
      ? ['.pdf', '.doc', '.docx', '.xlsx', '.xls']
      : ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png'];

  // Validate file size
  const sizeValidation = validateFileSize(file, minSizeMB);
  if (!sizeValidation.isValid && sizeValidation.error) {
    errors.push(sizeValidation.error);
  }

  // Validate file type
  const typeValidation = validateFileType(file, allowedTypes);
  if (!typeValidation.isValid && typeValidation.error) {
    errors.push(typeValidation.error);
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
