import {
  AlertCircle,
  Building2,
  Calendar,
  CheckCircle,
  FileText,
  Mail,
  Phone,
  Upload,
  Users,
} from 'lucide-react';
import { useState } from 'react';
import type { ContractorProfileData } from '../../hooks/use-contractor-profile';
import { Badge } from './badge';
import { Button } from './button';
import { Card, CardContent, CardHeader, CardTitle } from './card';
import { FileUploadModal } from './file-upload-modal';

interface ContractorDetailsProps {
  contractorData: ContractorProfileData;
  className?: string;
}

/**
 * Component to display contractor-specific details including company info and user details
 */
export function ContractorDetails({
  contractorData,
  className,
}: ContractorDetailsProps) {
  const { user, contractor } = contractorData;

  // State for certificate upload modals
  const [isRegistrationUploadOpen, setIsRegistrationUploadOpen] =
    useState(false);
  const [isLifUploadOpen, setIsLifUploadOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Handle certificate upload
  const handleCertificateUpload = async (
    files: File[],
    type: 'registration' | 'lif',
  ) => {
    setIsUploading(true);
    try {
      // TODO: Implement actual upload logic here
      // This should call your API to upload the files
      console.log(`Uploading ${type} certificate:`, files);

      // Simulate upload delay
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // TODO: Refresh contractor data after successful upload
      // You might want to call a refresh function or trigger a re-fetch
    } catch (error) {
      console.error('Upload failed:', error);
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  if (!user || !contractor) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-amber-600" />
            <span>Contractor Profile Incomplete</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Your contractor profile setup is not complete. Please complete your
            onboarding process.
          </p>
        </CardContent>
      </Card>
    );
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not specified';
    return new Date(dateString).toLocaleDateString('en-MY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getCompanyTypeDisplay = (type: string) => {
    switch (type) {
      case 'COMPETENT_FIRM':
        return 'Competent Firm';
      case 'NON_COMPETENT_FIRM':
        return 'Non-Competent Firm';
      case 'OEM':
        return 'OEM';
      default:
        return type;
    }
  };

  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* Company Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building2 className="h-5 w-5 text-blue-600" />
            <span>Company Information</span>
            {contractor.is_active && (
              <Badge variant="default" className="ml-2">
                <CheckCircle className="h-3 w-3 mr-1" />
                Active
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  Company Name
                </h4>
                <p className="font-medium">{contractor.name}</p>
              </div>

              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  Company Type
                </h4>
                <Badge variant="secondary">
                  {getCompanyTypeDisplay(contractor.contractor_type)}
                </Badge>
              </div>

              {contractor.code && (
                <div>
                  <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                    Company Code
                  </h4>
                  <p className="font-mono text-sm">{contractor.code}</p>
                </div>
              )}
            </div>

            <div className="space-y-3">
              {contractor.hotline && (
                <div>
                  <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                    Company Hotline
                  </h4>
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-green-600" />
                    <span>{contractor.hotline}</span>
                  </div>
                </div>
              )}

              {contractor.oem_name && (
                <div>
                  <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                    OEM Name
                  </h4>
                  <p>{contractor.oem_name}</p>
                </div>
              )}

              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  Registered
                </h4>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-blue-600" />
                  <span className="text-sm">
                    {formatDate(contractor.created_at)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* User Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-green-600" />
            <span>User Details</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  Full Name
                </h4>
                <p className="font-medium">{user.name}</p>
              </div>

              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  Email
                </h4>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-blue-600" />
                  <span>{user.email}</span>
                </div>
              </div>

              {user.phone_number && (
                <div>
                  <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                    Phone Number
                  </h4>
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-green-600" />
                    <span>{user.phone_number}</span>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-3">
              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  User Type
                </h4>
                <Badge variant="secondary">{user.user_role}</Badge>
              </div>

              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  Joined
                </h4>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-green-600" />
                  <span className="text-sm">{formatDate(user.created_at)}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documents Section - Placeholder for future file upload functionality */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-purple-600" />
            <span>Documents</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-4">
              Document management functionality will be available soon.
            </p>
            <div className="flex flex-col sm:flex-row gap-2 justify-center">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsRegistrationUploadOpen(true)}
                className="flex items-center space-x-2"
              >
                <Upload className="h-4 w-4" />
                <span>Upload Registration Certificate</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsLifUploadOpen(true)}
                className="flex items-center space-x-2"
              >
                <Upload className="h-4 w-4" />
                <span>Upload LIF List Files</span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Certificate Upload Modals */}
      <FileUploadModal
        isOpen={isRegistrationUploadOpen}
        onClose={() => setIsRegistrationUploadOpen(false)}
        onUpload={(files: File[]) =>
          handleCertificateUpload(files, 'registration')
        }
        title="Upload Registration Certificate"
        description="Upload a new registration certificate. Accepted formats: PDF, JPG, PNG (max 10MB)"
        maxFiles={5}
        acceptedTypes=".pdf,.jpg,.jpeg,.png"
        maxSize={10 * 1024 * 1024}
        isLoading={isUploading}
        folderPath="contractor/registration-certificates"
      />

      <FileUploadModal
        isOpen={isLifUploadOpen}
        onClose={() => setIsLifUploadOpen(false)}
        onUpload={(files: File[]) => handleCertificateUpload(files, 'lif')}
        title="Upload LIF List Files"
        description="Upload LIF list files. You can upload up to 5 files. Accepted formats: PDF, JPG, PNG (max 10MB each)"
        maxFiles={5}
        acceptedTypes=".pdf,.jpg,.jpeg,.png"
        maxSize={10 * 1024 * 1024}
        isLoading={isUploading}
        folderPath="contractor/lif-lists"
      />
    </div>
  );
}
