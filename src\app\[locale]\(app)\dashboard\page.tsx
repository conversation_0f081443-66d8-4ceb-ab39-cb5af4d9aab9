'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useLogout, useUserWithProfile } from '@/features/auth';
import { useProject } from '@/features/projects';
import { usePermissions } from '@/hooks/use-permissions';
import { useProjectContext } from '@/providers/project-context';
import {
  BarChart3,
  Building2,
  Calendar,
  Clock,
  FileText,
  Settings,
  Users,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function DashboardPage() {
  const { data: user, isLoading, error } = useUserWithProfile();
  const { userRole, isContractor, isJKR, isClient } = usePermissions();
  const { selectedProjectId, isInProjectContext } = useProjectContext();
  const { data: project, isLoading: projectLoading } = useProject(
    selectedProjectId || '',
  );
  const logoutMutation = useLogout();
  const router = useRouter();

  if (isLoading || (isInProjectContext && projectLoading)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">
          {isInProjectContext
            ? 'Loading project dashboard...'
            : 'Loading dashboard...'}
        </div>
      </div>
    );
  }

  if (error || !user) {
    router.push('/login');
    return null;
  }

  const handleLogout = async () => {
    try {
      await logoutMutation.mutateAsync();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  // Quick action cards based on user role
  const getQuickActions = () => {
    const actions = [];

    if (isJKR) {
      actions.push(
        { title: 'Manage Buildings', icon: Building2, href: '/buildings' },
        { title: 'View All Contractors', icon: Users, href: '/contractors' },
        { title: 'System Analytics', icon: BarChart3, href: '/analytics' },
        { title: 'System Settings', icon: Settings, href: '/settings' },
      );
    } else if (isContractor) {
      actions.push(
        { title: 'My Buildings', icon: Building2, href: '/buildings' },
        { title: 'Daily Reports', icon: FileText, href: '/reports' },
        { title: 'Analytics', icon: BarChart3, href: '/analytics' },
        { title: 'Profile Settings', icon: Settings, href: '/profile' },
      );
    } else if (isClient) {
      actions.push(
        { title: 'Daily Logs', icon: Clock, href: '/daily-logs' },
        { title: 'Submit Complaint', icon: FileText, href: '/complaints' },
        { title: 'My Profile', icon: Settings, href: '/profile' },
      );
    }

    return actions;
  };

  const quickActions = getQuickActions();

  // Project-specific dashboard when in project context
  if (isInProjectContext && project) {
    return (
      <div className="container mx-auto p-6 max-w-7xl">
        {/* Project Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              {project.name}
            </h1>
            <p className="text-muted-foreground mt-1">
              Project Dashboard - {project.code}
            </p>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="secondary">
                {project.status
                  ? project.status.charAt(0).toUpperCase() +
                    project.status.slice(1)
                  : 'Unknown'}
              </Badge>
              {project.state && (
                <Badge variant="outline">{project.state}</Badge>
              )}
            </div>
          </div>
          <Button
            onClick={handleLogout}
            variant="outline"
            disabled={logoutMutation.isPending}
          >
            {logoutMutation.isPending ? 'Signing out...' : 'Sign Out'}
          </Button>
        </div>

        {/* Project Overview Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Building2 className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">
                    Location
                  </p>
                  <p className="text-lg font-semibold">{project.location}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">
                    Start Date
                  </p>
                  <p className="text-lg font-semibold">
                    {project.start_date
                      ? new Date(project.start_date).toLocaleDateString()
                      : 'TBD'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">
                    End Date
                  </p>
                  <p className="text-lg font-semibold">
                    {project.end_date
                      ? new Date(project.end_date).toLocaleDateString()
                      : 'TBD'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-muted-foreground">
                    Agency
                  </p>
                  <p className="text-lg font-semibold">
                    {project.agency?.name || 'N/A'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Project Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Project Actions</CardTitle>
            <CardDescription>
              Quick access to project management features
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button
                variant="outline"
                className="h-20 flex flex-col gap-2 hover:bg-primary/5"
                onClick={() => router.push('/maintenance-logs')}
              >
                <Calendar className="h-6 w-6" />
                <span className="text-sm font-medium">Maintenance Logs</span>
              </Button>
              <Button
                variant="outline"
                className="h-20 flex flex-col gap-2 hover:bg-primary/5"
                onClick={() => router.push('/complaints')}
              >
                <FileText className="h-6 w-6" />
                <span className="text-sm font-medium">Complaints</span>
              </Button>
              <Button
                variant="outline"
                className="h-20 flex flex-col gap-2 hover:bg-primary/5"
                onClick={() => router.push('/members')}
              >
                <Users className="h-6 w-6" />
                <span className="text-sm font-medium">Project Members</span>
              </Button>
              <Button
                variant="outline"
                className="h-20 flex flex-col gap-2 hover:bg-primary/5"
                onClick={() => router.push('/analytics')}
              >
                <BarChart3 className="h-6 w-6" />
                <span className="text-sm font-medium">Analytics</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Project Activity */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Recent Project Activity</CardTitle>
            <CardDescription>Latest updates for this project</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-muted-foreground">
              <Clock className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p className="text-sm">No recent project activity</p>
              <p className="text-xs mt-1">
                Project activities will appear here as they happen
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Regular dashboard when not in project context

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            Welcome, {user.profile?.name || user.email?.split('@')[0]}!
          </h1>
          <p className="text-muted-foreground mt-1">
            Here&apos;s what&apos;s happening with your account today.
          </p>
          {userRole && (
            <Badge variant="secondary" className="mt-2">
              {userRole}
            </Badge>
          )}
        </div>
        <Button
          onClick={handleLogout}
          variant="outline"
          disabled={logoutMutation.isPending}
        >
          {logoutMutation.isPending ? 'Signing out...' : 'Sign Out'}
        </Button>
      </div>

      {/* Success message for completed onboarding */}
      <Card className="mb-6 border-green-200 bg-green-50">
        <CardContent className="pt-6">
          <div className="flex items-center gap-3">
            <div className="h-2 w-2 bg-green-500 rounded-full"></div>
            <p className="text-green-800 font-medium">
              🎉 Congratulations! Your onboarding is complete and you now have
              full access to the dashboard.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Dashboard Grid */}
      <div className="grid gap-6">
        {/* User Info Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Account Information
            </CardTitle>
            <CardDescription>Your account details and status</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Email
                </p>
                <p className="text-sm font-mono">{user.email}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Role
                </p>
                <p className="text-sm">{userRole}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Status
                </p>
                <Badge variant="default" className="text-xs">
                  {user.profile?.onboarding_completed
                    ? 'Active'
                    : 'Pending Setup'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and navigation based on your role
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="h-20 flex flex-col gap-2 hover:bg-primary/5"
                  onClick={() => router.push(action.href)}
                >
                  <action.icon className="h-6 w-6" />
                  <span className="text-sm font-medium">{action.title}</span>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity Placeholder */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Your latest actions and updates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-muted-foreground">
              <Clock className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p className="text-sm">No recent activity to display</p>
              <p className="text-xs mt-1">
                Start using the system to see your activity here
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
