/**
 * Example component showing how to implement JKR access control
 */

'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  useAccessibleProjects,
  useJKRMonitoringScope,
} from '@/hooks/use-project-access';
import { Building, Calendar, MapPin } from 'lucide-react';

export function ProjectDashboard() {
  const { data: projects, isLoading, error } = useAccessibleProjects();
  const { canViewAllStates, monitoringState, isJKRUser } =
    useJKRMonitoringScope();

  if (isLoading) {
    return <div>Loading projects...</div>;
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Failed to load projects. Please try again.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Access Control Info for JKR Users */}
      {isJKRUser && (
        <Alert>
          <AlertDescription>
            {canViewAllStates
              ? 'You have admin access and can view projects from all states.'
              : `You can view projects from ${monitoringState} state only.`}
          </AlertDescription>
        </Alert>
      )}

      {/* Projects Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {projects?.map((project) => (
          <Card key={project.id}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="truncate">{project.name}</span>
                <Badge variant="outline">{project.status}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Building className="h-4 w-4" />
                <span>Code: {project.code}</span>
              </div>

              {project.state && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <MapPin className="h-4 w-4" />
                  <span>State: {project.state}</span>
                </div>
              )}

              {project.location && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <MapPin className="h-4 w-4" />
                  <span>{project.location}</span>
                </div>
              )}

              {project.start_date && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  <span>
                    Start: {new Date(project.start_date).toLocaleDateString()}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {projects?.length === 0 && (
        <Alert>
          <AlertDescription>
            No projects found for your access level.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
