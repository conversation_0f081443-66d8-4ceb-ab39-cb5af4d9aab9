{"i18n-ally.localesPaths": ["messages", "src/i18n"], "typescript.preferences.checkJs": true, "typescript.reportStyleChecksAsWarnings": true, "eslint.enable": true, "eslint.run": "onType", "eslint.format.enable": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "files.associations": {"*.css": "tailwindcss"}, "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "git.enableCommitSigning": false, "git.alwaysSignOff": false, "problems.decorations.enabled": true, "typescript.validate.enable": true, "javascript.validate.enable": true}