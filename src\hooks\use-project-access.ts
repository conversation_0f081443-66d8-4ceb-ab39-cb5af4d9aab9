/**
 * Custom hook for project access control
 * This demonstrates how to implement the JKR state-based access control
 */

import { useUserWithProfile } from '@/hooks/use-auth';
import { supabase } from '@/lib/supabase';
import { AccessControl, Project } from '@/types/access-control';
import { useQuery } from '@tanstack/react-query';

/**
 * Hook to fetch projects based on user's access level
 */
export function useAccessibleProjects() {
  const { data: user } = useUserWithProfile(); // Get current user with profile

  return useQuery({
    queryKey: [
      'accessible-projects',
      user?.id,
      user?.profile?.user_role,
      user?.profile?.monitoring_state,
    ],
    queryFn: async (): Promise<Project[]> => {
      if (!user?.profile) return [];

      // Build the query
      let query = supabase
        .from('projects')
        .select(
          `
          id,
          name,
          code,
          state,
          location,
          start_date,
          end_date,
          status,
          contractor_id,
          agency_id
        `,
        )
        .is('deleted_at', null); // Only active projects

      // Apply access control filter
      if (user.profile.user_role === 'admin') {
        // Admin users: check their access mode for filtering
        if (
          user.profile.admin_access_mode === 'state' &&
          user.profile.monitoring_state
        ) {
          // State-based access (equivalent to old JKR_PIC)
          query = query.eq('state', user.profile.monitoring_state);
        }
        // Project-based access (equivalent to old JKR_Admin) gets all projects (no additional filter)
      } else if (
        user.profile.user_role === 'contractor' &&
        user.profile.contractor_id
      ) {
        query = query.eq('contractor_id', user.profile.contractor_id);
      }
      // Viewer users would need specific access rules if they can view projects

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch projects: ${error.message}`);
      }

      return data || [];
    },
    enabled: !!user,
  });
}

/**
 * Hook to check if current user can view a specific project
 */
export function useCanViewProject(projectId: string) {
  const { data: user } = useUserWithProfile();

  return useQuery({
    queryKey: ['can-view-project', user?.id, projectId],
    queryFn: async (): Promise<boolean> => {
      if (!user?.profile || !projectId) return false;

      // Fetch the project
      const { data: project, error } = await supabase
        .from('projects')
        .select(
          'id, name, code, state, location, start_date, end_date, status, contractor_id, agency_id',
        )
        .eq('id', projectId)
        .is('deleted_at', null)
        .single();

      if (error || !project) return false;

      // Transform user to match AccessControl interface
      const accessControlUser = {
        id: user.id,
        name: user.profile.name || '',
        email: user.email || '',
        user_role: user.profile.user_role,
        admin_access_mode: user.profile.admin_access_mode,
        monitoring_state: user.profile.monitoring_state,
        contractor_id: user.profile.contractor_id,
      };

      // Check access using our access control utility
      return AccessControl.canUserViewProject(accessControlUser, project);
    },
    enabled: !!user && !!projectId,
  });
}

/**
 * Hook specifically for JKR users to get their monitoring scope
 */
export function useJKRMonitoringScope() {
  const { data: user } = useUserWithProfile();

  const scope = {
    canViewAllStates:
      user?.profile?.user_role === 'admin' &&
      user?.profile?.admin_access_mode === 'project',
    monitoringState:
      user?.profile?.user_role === 'admin' &&
      user?.profile?.admin_access_mode === 'state'
        ? user.profile.monitoring_state
        : null,
    isJKRUser: user?.profile?.user_role === 'admin',
  };

  return scope;
}
