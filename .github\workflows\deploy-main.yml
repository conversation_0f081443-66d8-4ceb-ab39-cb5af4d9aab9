name: Build and deploy Node.js app to Azure Web App - simple-fe

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  migrate-production:
    name: Run Database Migrations (Production)
    runs-on: ubuntu-latest
    permissions:
      contents: read
    env:
      SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
      SUPABASE_DB_PASSWORD: ${{ secrets.SUPABASE_DB_PASSWORD }}
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 8

      - name: Cache pnpm modules
        uses: actions/cache@v4
        with:
          path: ~/.pnpm-store
          key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-

      - name: Install dependencies
        run: pnpm install

      - name: Clean previous Supabase link
        run: supabase unlink || true

      - name: Link to Supabase project
        run: |
          echo "Linking to production Supabase project..."
          supabase link --project-ref ${{ secrets.SUPABASE_PROJECT_REF }}

      - name: Check migration status
        run: |
          echo "Checking current migration status..."
          supabase migration list --linked

      - name: Run database migrations
        run: |
          echo "Applying pending migrations to production database..."
          supabase db push

      - name: Deploy email templates and configuration
        run: |
          echo "Deploying email templates and config to production..."
          echo "⚠️  Note: Site URLs and redirect URLs should be configured manually in Supabase Dashboard"
          echo "   -> Authentication -> URL Configuration"
          echo "   -> Add: https://simple-fe.azurewebsites.net"
          supabase config push

      - name: Generate updated database types
        run: |
          echo "Generating updated database types..."
          supabase gen types typescript --linked > src/types/database.ts

      - name: Upload database types artifact
        uses: actions/upload-artifact@v4
        with:
          name: database-types
          path: src/types/database.ts

  build:
    needs: migrate-production
    name: Build
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4

      - name: Download database types
        uses: actions/download-artifact@v4
        with:
          name: database-types
          path: src/types/

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 8

      - name: Cache pnpm modules
        uses: actions/cache@v4
        with:
          path: ~/.pnpm-store
          key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-

      - name: Create .env.local file
        run: |
          echo "NEXT_PUBLIC_SUPABASE_URL=${{ secrets.SUPABASE_URL }}" >> .env.local
          echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ secrets.SUPABASE_ANON_KEY }}" >> .env.local
          echo "TMONE_BUCKET=simple-obs" >> .env.local
          echo "TMONE_ACCESS_KEY_ID=QN97RS7DLWSLOFSJMSDR" >> .env.local
          echo "TMONE_SECRET_ACCESS_KEY=7FLelJ8IqiwSWKg1LSs8wBJfMsNPRLBccm0OVFl2" >> .env.local
          echo "TMONE_SERVER=https://obs.my-kualalumpur-1.alphaedge.tmone.com.my" >> .env.local
          echo "TMONE_OBS_STORAGE=https://simple-obs.obs.my-kualalumpur-1.alphaedge.tmone.com.my/" >> .env.local
          echo "OBS_EXPIRY_TIME=3600" >> .env.local

      - name: Install dependencies
        run: pnpm install

      - name: Build app
        run: pnpm run build

      - name: Prepare standalone artifact
        run: |
          cp .env.local build/standalone/
          cp -r build/static build/standalone/build/static
          cp web.config build/standalone/
          cp startup.sh build/standalone/
          mkdir -p build/standalone/supabase
          cp -r supabase/* build/standalone/supabase/
          chmod +x build/standalone/startup.sh

      - name: Zip build artifact (standalone)
        run: |
          cd build/standalone
          zip -r ../../release.zip .
        working-directory: .

      - uses: actions/upload-artifact@v4
        with:
          name: node-app
          path: release.zip

  deploy-production:
    name: Deploy to Azure Web App (Production)
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: 'production'
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: node-app

      - name: Unzip artifact for deployment
        run: unzip release.zip

      - name: Login to Azure
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.AZUREAPPSERVICE_CLIENTID }}
          tenant-id: ${{ secrets.AZUREAPPSERVICE_TENANTID }}
          subscription-id: ${{ secrets.AZUREAPPSERVICE_SUBSCRIPTIONID }}

      - name: Deploy to Azure Web App
        id: deploy-to-webapp
        uses: azure/webapps-deploy@v3
        with:
          app-name: 'simple-fe'
          slot-name: 'Production'
          package: .
