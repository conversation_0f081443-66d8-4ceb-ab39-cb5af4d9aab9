import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup } from '@/components/ui/radio-group';
import { SectionHeader } from '@/components/ui/section-header';
import {
  useCommonTranslations,
  useContractorTranslations,
} from '@/hooks/use-translations';
import { Building2, ChevronLeft, ChevronRight, Users } from 'lucide-react';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { type ContractorStep2FormValues } from '../schemas/contractor-onboarding-schemas';
import { CompanyRegistrationOption } from './company-registration-option';

interface ContractorOnboardingStep3Props {
  form: UseFormReturn<ContractorStep2FormValues>;
  onSubmit: (values: ContractorStep2FormValues) => void;
  onPrevious: () => void;
  companyRegistrationType: 'create' | 'join';
}

export const ContractorOnboardingStep3 =
  React.memo<ContractorOnboardingStep3Props>(
    ({ form, onSubmit, onPrevious, companyRegistrationType }) => {
      const t = useContractorTranslations();
      const tCommon = useCommonTranslations();

      return (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-7">
            <SectionHeader number={2} title={t('onboarding.step3.title')} />

            {/* Company Registration Type Selection */}
            <FormField
              control={form.control}
              name="companyRegistrationType"
              render={({ field }) => (
                <FormItem className="space-y-6">
                  <FormLabel className="text-base font-semibold">
                    {t('onboarding.step3.registrationType')}
                  </FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="space-y-4"
                    >
                      <CompanyRegistrationOption
                        value="create"
                        id="create-company"
                        icon={
                          <Building2 className="w-5 h-5 text-primary-foreground" />
                        }
                        title={t('onboarding.step3.createTitle')}
                        description={t('onboarding.step3.createDescription')}
                      />
                      <CompanyRegistrationOption
                        value="join"
                        id="join-company"
                        icon={
                          <Users className="w-5 h-5 text-primary-foreground" />
                        }
                        title={t('onboarding.step3.joinTitle')}
                        description={t('onboarding.step3.joinDescription')}
                      />
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Special Code Field - Only show when joining a company */}
            {companyRegistrationType === 'join' && (
              <FormField
                control={form.control}
                name="specialCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t('onboarding.step3.specialCode')}{' '}
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t(
                          'onboarding.step3.specialCodePlaceholder',
                        )}
                        {...field}
                      />
                    </FormControl>
                    <p className="text-xs text-muted-foreground">
                      {t('onboarding.step3.specialCodeHelp')}
                    </p>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Action Buttons */}
            <div className="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={onPrevious}
                className="px-6 py-3"
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                {tCommon('back')}
              </Button>

              <Button
                type="submit"
                className="px-8 py-3"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting
                  ? tCommon('loading')
                  : companyRegistrationType === 'create'
                    ? t('onboarding.step3.continueToCreation')
                    : t('onboarding.step3.joinCompanyButton')}
                <ChevronRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </form>
        </Form>
      );
    },
  );

ContractorOnboardingStep3.displayName = 'ContractorOnboardingStep3';
