import { supabase } from '@/lib/supabase';
import type {
  CreateProfilePayload,
  Profile,
  ProfileInsert,
  ProfileUpdate,
  UserRole,
} from '@/types/auth';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

// Hook to get user profile by ID
export function useProfile(userId?: string) {
  return useQuery({
    queryKey: ['profile', userId],
    queryFn: async () => {
      if (!userId) throw new Error('User ID is required');

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      return data as Profile;
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to create a user profile
export function useCreateProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['profile', 'create'],
    mutationFn: async (profileData: CreateProfilePayload) => {
      const insertData: ProfileInsert = {
        id: profileData.id,
        email: profileData.email,
        name: profileData.name,
        phone_number: profileData.phone_number,
        user_role: profileData.user_role,
        created_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('users')
        .insert(insertData)
        .select()
        .single();

      if (error) throw error;
      return data as Profile;
    },
    onSuccess: (data) => {
      // Update the profile cache
      queryClient.setQueryData(['profile', data.id], data);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['profile'] });
    },
  });
}

// Hook to update user profile
export function useUpdateProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['profile', 'update'],
    mutationFn: async ({
      userId,
      updates,
    }: {
      userId: string;
      updates: Partial<ProfileUpdate>;
    }) => {
      const { data, error } = await supabase
        .from('users')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return data as Profile;
    },
    onSuccess: (data) => {
      // Update the profile cache
      queryClient.setQueryData(['profile', data.id], data);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['profile'] });
    },
  });
}

// Hook to get profiles by role
export function useProfilesByRole(role?: UserRole) {
  return useQuery({
    queryKey: ['profiles', 'by-role', role],
    queryFn: async () => {
      if (!role) throw new Error('Role is required');

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('user_role', role)
        .is('deleted_at', null) // Only get non-deleted profiles
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Profile[];
    },
    enabled: !!role,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook to soft delete a profile
export function useDeleteProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['profile', 'delete'],
    mutationFn: async (userId: string) => {
      const { data, error } = await supabase
        .from('users')
        .update({
          deleted_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return data as Profile;
    },
    onSuccess: (data) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: ['profile', data.id] });

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['profiles'] });
    },
  });
}
