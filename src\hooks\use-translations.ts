import { useTranslations } from 'next-intl';

/**
 * Custom hook for common translations
 */
export function useCommonTranslations() {
  return useTranslations('common');
}

/**
 * Custom hook for navigation translations
 */
export function useNavigationTranslations() {
  return useTranslations('navigation');
}

/**
 * Custom hook for auth translations
 */
export function useAuthTranslations() {
  return useTranslations('auth');
}

/**
 * Custom hook for contractor translations
 */
export function useContractorTranslations() {
  return useTranslations('contractor');
}

/**
 * Custom hook for company translations
 */
export function useCompanyTranslations() {
  return useTranslations('company');
}

/**
 * Custom hook for validation translations
 */
export function useValidationTranslations() {
  return useTranslations('validation');
}

/**
 * Custom hook for error translations
 */
export function useErrorTranslations() {
  return useTranslations('errors');
}

/**
 * Custom hook for dashboard translations
 */
export function useDashboardTranslations() {
  return useTranslations('dashboard');
}

/**
 * Custom hook for agency translations
 */
export function useAgencyTranslations() {
  return useTranslations('agencies');
}

/**
 * Custom hook for state translations
 */
export function useStateTranslations() {
  return useTranslations('states');
}
